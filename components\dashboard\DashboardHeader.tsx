"use client";

import { useState } from "react";
import { 
  Bars3Icon, 
  BellIcon, 
  MagnifyingGlassIcon,
  PlusIcon,
  ChevronDownIcon
} from "@heroicons/react/24/outline";
import ButtonAccount from "@/components/ButtonAccount";

export default function DashboardHeader() {
  const [searchQuery, setSearchQuery] = useState("");

  return (
    <header className="bg-base-100 border-b border-base-300 sticky top-0 z-40">
      <div className="flex items-center justify-between px-4 py-3 lg:px-6">
        {/* Left side - Mobile menu button and search */}
        <div className="flex items-center gap-4 flex-1">
          {/* Mobile menu button */}
          <label htmlFor="drawer-toggle" className="btn btn-ghost btn-sm lg:hidden">
            <Bars3Icon className="w-5 h-5" />
          </label>

          {/* Search bar */}
          <div className="relative flex-1 max-w-md">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-base-content/40" />
            </div>
            <input
              type="text"
              placeholder="Search recipes, ingredients..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="input input-bordered w-full pl-10 pr-4 py-2 text-sm bg-base-200 border-base-300 focus:border-warning focus:ring-warning"
            />
          </div>
        </div>

        {/* Right side - Actions and user menu */}
        <div className="flex items-center gap-3">
          {/* Quick add recipe button */}
          <div className="dropdown dropdown-end">
            <label tabIndex={0} className="btn btn-warning btn-sm gap-2">
              <PlusIcon className="w-4 h-4" />
              <span className="hidden sm:inline">Add</span>
              <ChevronDownIcon className="w-3 h-3" />
            </label>
            <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 rounded-box w-52 border border-base-300">
              <li>
                <a href="/dashboard/recipes/add" className="text-sm">
                  <PlusIcon className="w-4 h-4" />
                  Add Recipe
                </a>
              </li>
              <li>
                <a href="/dashboard/recipes/import" className="text-sm">
                  <MagnifyingGlassIcon className="w-4 h-4" />
                  Import from URL
                </a>
              </li>
              <li>
                <a href="/dashboard/meal-planner" className="text-sm">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  Plan Meal
                </a>
              </li>
            </ul>
          </div>

          {/* Notifications */}
          <div className="dropdown dropdown-end">
            <label tabIndex={0} className="btn btn-ghost btn-sm btn-circle">
              <div className="indicator">
                <BellIcon className="w-5 h-5" />
                <span className="badge badge-xs badge-error indicator-item"></span>
              </div>
            </label>
            <div tabIndex={0} className="dropdown-content z-[1] card card-compact w-64 p-2 shadow-lg bg-base-100 border border-base-300">
              <div className="card-body">
                <h3 className="font-bold text-sm">Notifications</h3>
                <div className="space-y-2">
                  <div className="text-xs text-base-content/60 p-2 bg-base-200 rounded">
                    Your meal plan for this week is ready!
                  </div>
                  <div className="text-xs text-base-content/60 p-2 bg-base-200 rounded">
                    3 new recipes match your dietary preferences
                  </div>
                </div>
                <div className="card-actions">
                  <button className="btn btn-ghost btn-xs">View all</button>
                </div>
              </div>
            </div>
          </div>

          {/* User account */}
          <ButtonAccount />
        </div>
      </div>
    </header>
  );
}
