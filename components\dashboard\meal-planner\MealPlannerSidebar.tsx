"use client";

import { useState } from "react";
import { 
  MagnifyingGlassIcon,
  BookOpenIcon,
  HeartIcon,
  ClockIcon,
  FunnelIcon
} from "@heroicons/react/24/outline";

// Mock recipe data for the sidebar
const mockRecipes = [
  {
    id: 1,
    name: "Honey Garlic Chicken",
    image: "/Chicken.png",
    cookTime: "30 min",
    difficulty: "Easy",
    category: "Dinner",
    isFavorite: true
  },
  {
    id: 2,
    name: "Chocolate Banana Bread",
    image: "/Banana-Bread.png",
    cookTime: "1h 15min",
    difficulty: "Medium",
    category: "Dessert",
    isFavorite: false
  },
  {
    id: 3,
    name: "Fresh Garden Salad",
    image: "/Salad.png",
    cookTime: "15 min",
    difficulty: "Easy",
    category: "Lunch",
    isFavorite: true
  },
  {
    id: 4,
    name: "Spicy Jerk Chicken",
    image: "/Jerk-Chicken.png",
    cookTime: "45 min",
    difficulty: "Medium",
    category: "Dinner",
    isFavorite: false
  },
  {
    id: 5,
    name: "Classic Margherita Pizza",
    image: "/Pizza.png",
    cookTime: "25 min",
    difficulty: "Hard",
    category: "Dinner",
    isFavorite: true
  }
];

const categories = ['All', 'Breakfast', 'Lunch', 'Dinner', 'Dessert', 'Snack'];

export default function MealPlannerSidebar() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const [showFavoritesOnly, setShowFavoritesOnly] = useState(false);

  const filteredRecipes = mockRecipes.filter(recipe => {
    const matchesSearch = recipe.name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === "All" || recipe.category === selectedCategory;
    const matchesFavorites = !showFavoritesOnly || recipe.isFavorite;
    
    return matchesSearch && matchesCategory && matchesFavorites;
  });

  const handleDragStart = (e: React.DragEvent, recipe: any) => {
    e.dataTransfer.setData("application/json", JSON.stringify(recipe));
    e.dataTransfer.effectAllowed = "copy";
  };

  return (
    <div className="bg-base-200 rounded-xl border border-base-300 h-fit">
      {/* Header */}
      <div className="p-4 border-b border-base-300">
        <div className="flex items-center gap-2 mb-4">
          <BookOpenIcon className="w-5 h-5 text-warning" />
          <h2 className="font-semibold text-base-content">Recipe Library</h2>
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-4 w-4 text-base-content/40" />
          </div>
          <input
            type="text"
            placeholder="Search recipes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="input input-bordered input-sm w-full pl-10 bg-base-100 border-base-300 focus:border-warning"
          />
        </div>

        {/* Filters */}
        <div className="space-y-2">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="select select-bordered select-sm w-full bg-base-100 border-base-300 focus:border-warning"
          >
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>

          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={showFavoritesOnly}
              onChange={(e) => setShowFavoritesOnly(e.target.checked)}
              className="checkbox checkbox-warning checkbox-sm"
            />
            <span className="text-sm text-base-content">Favorites only</span>
          </label>
        </div>
      </div>

      {/* Recipe List */}
      <div className="p-4">
        <div className="space-y-3 max-h-96 overflow-y-auto">
          {filteredRecipes.length === 0 ? (
            <div className="text-center py-8 text-base-content/60">
              <BookOpenIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No recipes found</p>
            </div>
          ) : (
            filteredRecipes.map((recipe) => (
              <div
                key={recipe.id}
                draggable
                onDragStart={(e) => handleDragStart(e, recipe)}
                className="bg-base-100 rounded-lg p-3 border border-base-300 cursor-grab hover:shadow-md transition-shadow"
              >
                <div className="flex gap-3">
                  <div className="w-12 h-12 bg-base-300 rounded-lg overflow-hidden flex-shrink-0">
                    <img 
                      src={recipe.image} 
                      alt={recipe.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <h3 className="font-medium text-sm text-base-content line-clamp-1">
                        {recipe.name}
                      </h3>
                      {recipe.isFavorite && (
                        <HeartIcon className="w-4 h-4 text-error flex-shrink-0 ml-1" />
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2 mt-1 text-xs text-base-content/60">
                      <div className="flex items-center gap-1">
                        <ClockIcon className="w-3 h-3" />
                        <span>{recipe.cookTime}</span>
                      </div>
                      <span>•</span>
                      <span>{recipe.difficulty}</span>
                    </div>
                    
                    <div className="mt-1">
                      <span className="badge badge-xs badge-outline">
                        {recipe.category}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Drag Instructions */}
        <div className="mt-4 p-3 bg-warning/10 rounded-lg border border-warning/20">
          <p className="text-xs text-base-content/70 text-center">
            💡 Drag recipes to calendar slots to add them to your meal plan
          </p>
        </div>
      </div>
    </div>
  );
}
