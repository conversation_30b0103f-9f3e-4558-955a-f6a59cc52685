import Link from "next/link";
import { getSEOTags } from "@/libs/seo";
import Image from "next/image";
import config from "@/config";
import SocialSubscribe from "@/components/SocialSubscribe";
import Footer from "@/components/Footer";

// SEO Metadata for the About Page
export const metadata = getSEOTags({
  title: `About | ${config.appName}`,
  canonicalUrlRelative: '/about',
  description: "Discover the mission, story, and team behind OVEN350. Learn how we're revolutionizing the modern kitchen.",
});

// The About Page Component
const About = () => {

  return (
    <>
      <main className="max-w-4xl mx-auto">
        <div className="p-5 md:p-8">
          <Link href="/" className="btn btn-ghost mb-4">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
              className="w-5 h-5"
            >
              <path
                fillRule="evenodd"
                d="M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z"
                clipRule="evenodd"
              />
            </svg>
            Back
          </Link>
          
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-extrabold pb-4">
              About {config.appName}
            </h1>
            <p className="text-lg text-base-content/80 max-w-2xl mx-auto">
              We believe cooking should be an act of joy, not a daily chore. We're here to build the brain for your kitchen, connecting inspiration to execution seamlessly.
            </p>
          </div>

          <div className="space-y-12 md:space-y-16">
            
            {/* Our Story Section */}
            <section className="text-base-content/90 leading-relaxed">
              <h2 className="text-3xl font-bold text-center mb-6">Our Story</h2>
              <div className="prose prose-lg max-w-none mx-auto text-center">
                <p>
                  {config.appName} started in a busy New Jersey kitchen, surrounded by recipe binders, countless browser tabs, and the frustrating beep of a "smart" oven that felt anything but. We knew there had to be a better way. Why couldn't our recipes talk to our meal plan? Why couldn't our meal plan create a grocery list automatically? And why couldn't our smart appliances just... work, without needing five different apps?
                </p>
                <p>
                  Driven by this frustration, we set out to build the one platform we always wished we had: a single, elegant command center for the entire cooking process. A place where culinary inspiration meets intelligent automation. That's how {config.appName} was born.
                </p>
              </div>
            </section>

            {/* The Problem We Solve Section */}
            <section className="bg-base-200/50 p-8 rounded-2xl">
              <h2 className="text-3xl font-bold text-center mb-6">Ending Kitchen Chaos</h2>
              <div className="grid md:grid-cols-2 gap-8 text-base-content/80">
                <div>
                  <h3 className="text-xl font-semibold mb-2">The Old Way</h3>
                  <ul className="list-disc list-inside space-y-2">
                    <li>Hours wasted searching for recipes</li>
                    <li>Last-minute stress about "what's for dinner?"</li>
                    <li>Food waste from buying forgotten ingredients</li>
                    <li>The frustration of unused, "dumb" smart devices</li>
                  </ul>
                </div>
                <div>
                  <h3 className="text-xl font-semibold mb-2">The {config.appName} Way</h3>
                  <ul className="list-disc list-inside space-y-2 text-warning">
                    <li>Your entire recipe collection, instantly searchable</li>
                    <li>Stress-free weekly meal plans in minutes</li>
                    <li>Automated grocery lists that save you time and money</li>
                    <li>A truly connected and intelligent kitchen experience</li>
                  </ul>
                </div>
              </div>
            </section>
            
            {/* Meet the Team Section (using placeholders) */}
            <section>
              <h2 className="text-3xl font-bold text-center mb-8">Our Culinary Innovators</h2>
              <div className="flex flex-col md:flex-row justify-center items-center gap-8 md:gap-12">
                
                {/* Team Member 1 Placeholder */}
                <div className="text-center">
                  <Image 
                    src="/vanessa.png" 
                    alt="Team Member 1" 
                    width={128} 
                    height={128}
                    className="rounded-full mx-auto mb-4"
                  />
                  <h4 className="text-xl font-bold">Vanessa Coquillo</h4>
                  <p className="text-base-content/70">Co-Founder & CEO</p>
                </div>
                
                {/* Team Member 2 Placeholder */}
                <div className="text-center">
                  <Image 
                    src="/thory.png" 
                    alt="Team Member 2" 
                    width={128} 
                    height={128}
                    className="rounded-full mx-auto mb-4"
                  />
                  <h4 className="text-xl font-bold">Thory Coquillo</h4>
                  <p className="text-base-content/70">Co-Founder & CTO</p>
                </div>

              </div>
            </section>

          </div>
        </div>
      </main>
      {/* The interactive SocialSubscribe Client Component is rendered here */}
      <SocialSubscribe />
      <Footer />
    </>
  );

};

export default About;
