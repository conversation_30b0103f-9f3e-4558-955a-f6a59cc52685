export interface Ingredient {
  name: string;
  amount: number;
  unit: string;
  notes?: string;
}

export interface Instruction {
  stepNumber: number;
  instruction: string;
  duration?: number;
  temperature?: number;
}

export interface RecipeFormData {
  name: string;
  description: string;
  servings: number;
  prepTime: number;
  cookTime: number;
  difficulty: "Easy" | "Medium" | "Hard";
  category: string;
  cuisine: string;
  ingredients: Ingredient[];
  instructions: Instruction[];
  tags: string[];
  dietaryRestrictions: string[];
  images: File | string | null;
}
