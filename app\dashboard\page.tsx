import { auth } from "@/libs/next-auth";
import DashboardOverview from "@/components/dashboard/DashboardOverview";

export const dynamic = "force-dynamic";

// This is a private page: It's protected by the layout.js component which ensures the user is authenticated.
// It's a server component which means you can fetch data (like the user profile) before the page is rendered.
export default async function Dashboard() {
  const session = await auth();

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-gradient-to-r from-warning/10 to-warning/5 rounded-xl p-6 border border-warning/20">
        <h1 className="text-2xl lg:text-3xl font-bold text-base-content mb-2">
          Welcome back, {session?.user?.name || "Chef"}! 👨‍🍳
        </h1>
        <p className="text-base-content/70">
          Ready to create something delicious? Let's get cooking with your personalized recipe management system.
        </p>
      </div>

      {/* Dashboard Overview */}
      <DashboardOverview />
    </div>
  );
}
