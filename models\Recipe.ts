import mongoose from "mongoose";
import toJ<PERSON><PERSON> from "./plugins/toJSON";

// Ingredient schema for recipe ingredients
const ingredientSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 0,
  },
  unit: {
    type: String,
    required: true,
    trim: true,
  },
  notes: {
    type: String,
    trim: true,
  },
  category: {
    type: String,
    trim: true,
    enum: ['protein', 'vegetable', 'fruit', 'grain', 'dairy', 'spice', 'condiment', 'other'],
    default: 'other',
  },
});

// Instruction step schema
const instructionSchema = new mongoose.Schema({
  stepNumber: {
    type: Number,
    required: true,
    min: 1,
  },
  instruction: {
    type: String,
    required: true,
    trim: true,
  },
  duration: {
    type: Number, // in minutes
    min: 0,
  },
  temperature: {
    type: Number, // in Fahrenheit
    min: 0,
  },
  image: {
    type: String, // URL to step image
    trim: true,
  },
});

// Nutrition information schema
const nutritionSchema = new mongoose.Schema({
  calories: { type: Number, min: 0 },
  protein: { type: Number, min: 0 }, // in grams
  carbohydrates: { type: Number, min: 0 }, // in grams
  fat: { type: Number, min: 0 }, // in grams
  fiber: { type: Number, min: 0 }, // in grams
  sugar: { type: Number, min: 0 }, // in grams
  sodium: { type: Number, min: 0 }, // in mg
  cholesterol: { type: Number, min: 0 }, // in mg
});

// Rating schema for user ratings
const ratingSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5,
  },
  review: {
    type: String,
    trim: true,
    maxlength: 1000,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
});

// Main Recipe schema
const recipeSchema = new mongoose.Schema(
  {
    // Basic Information
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    
    // Recipe Details
    servings: {
      type: Number,
      required: true,
      min: 1,
      max: 100,
    },
    prepTime: {
      type: Number, // in minutes
      required: true,
      min: 0,
    },
    cookTime: {
      type: Number, // in minutes
      required: true,
      min: 0,
    },
    totalTime: {
      type: Number, // in minutes (calculated field)
      min: 0,
    },
    
    // Difficulty and Category
    difficulty: {
      type: String,
      required: true,
      enum: ['Easy', 'Medium', 'Hard'],
      default: 'Medium',
    },
    category: {
      type: String,
      required: true,
      enum: ['Breakfast', 'Lunch', 'Dinner', 'Dessert', 'Snack', 'Beverage', 'Appetizer'],
    },
    cuisine: {
      type: String,
      trim: true,
      enum: ['American', 'Italian', 'Mexican', 'Asian', 'Mediterranean', 'Indian', 'French', 'Caribbean', 'Other'],
      default: 'Other',
    },
    
    // Recipe Content
    ingredients: [ingredientSchema],
    instructions: [instructionSchema],
    
    // Media
    images: [{
      url: { type: String, required: true },
      alt: { type: String, trim: true },
      isPrimary: { type: Boolean, default: false },
    }],
    video: {
      url: { type: String, trim: true },
      thumbnail: { type: String, trim: true },
    },
    
    // Tags and Dietary Information
    tags: [{
      type: String,
      trim: true,
      lowercase: true,
    }],
    dietaryRestrictions: [{
      type: String,
      enum: ['vegetarian', 'vegan', 'gluten-free', 'dairy-free', 'nut-free', 'keto', 'paleo', 'low-carb', 'low-fat'],
    }],
    
    // Nutrition (optional)
    nutrition: nutritionSchema,
    
    // User and Sharing
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    isPublic: {
      type: Boolean,
      default: false,
    },
    isOriginal: {
      type: Boolean,
      default: true, // false if imported from external source
    },
    sourceUrl: {
      type: String, // URL if imported from website
      trim: true,
    },
    sourceName: {
      type: String, // Name of source (website, cookbook, etc.)
      trim: true,
    },
    
    // Ratings and Reviews
    ratings: [ratingSchema],
    averageRating: {
      type: Number,
      min: 0,
      max: 5,
      default: 0,
    },
    totalRatings: {
      type: Number,
      min: 0,
      default: 0,
    },
    
    // Usage Statistics
    timesCooked: {
      type: Number,
      min: 0,
      default: 0,
    },
    lastCooked: {
      type: Date,
    },
    
    // Favorites
    favoritedBy: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    }],
    
    // Status
    status: {
      type: String,
      enum: ['draft', 'published', 'archived'],
      default: 'published',
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

// Indexes for better query performance
recipeSchema.index({ userId: 1, status: 1 });
recipeSchema.index({ category: 1, cuisine: 1 });
recipeSchema.index({ tags: 1 });
recipeSchema.index({ dietaryRestrictions: 1 });
recipeSchema.index({ averageRating: -1 });
recipeSchema.index({ createdAt: -1 });
recipeSchema.index({ name: 'text', description: 'text', tags: 'text' });

// Virtual for total time calculation
recipeSchema.virtual('calculatedTotalTime').get(function() {
  return this.prepTime + this.cookTime;
});

// Pre-save middleware to calculate total time
recipeSchema.pre('save', function(next) {
  this.totalTime = this.prepTime + this.cookTime;
  next();
});

// Method to calculate average rating
recipeSchema.methods.calculateAverageRating = function() {
  if (this.ratings.length === 0) {
    this.averageRating = 0;
    this.totalRatings = 0;
  } else {
    const sum = this.ratings.reduce((acc, rating) => acc + rating.rating, 0);
    this.averageRating = Math.round((sum / this.ratings.length) * 10) / 10;
    this.totalRatings = this.ratings.length;
  }
};

// Method to add rating
recipeSchema.methods.addRating = function(userId: string, rating: number, review?: string) {
  // Remove existing rating from this user
  this.ratings = this.ratings.filter(r => r.userId.toString() !== userId);
  
  // Add new rating
  this.ratings.push({ userId, rating, review });
  
  // Recalculate average
  this.calculateAverageRating();
};

// Method to toggle favorite
recipeSchema.methods.toggleFavorite = function(userId: string) {
  const index = this.favoritedBy.indexOf(userId);
  if (index > -1) {
    this.favoritedBy.splice(index, 1);
  } else {
    this.favoritedBy.push(userId);
  }
};

// Add plugin that converts mongoose to json
recipeSchema.plugin(toJSON);

export default mongoose.models.Recipe || mongoose.model("Recipe", recipeSchema);
