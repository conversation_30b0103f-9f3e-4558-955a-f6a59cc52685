import Image from "next/image";
import TestimonialsAvatars from "./TestimonialsAvatars";
import config from "@/config";

const Hero = () => {
  return (
    <section className="max-w-7xl mx-auto bg-base-100 flex flex-col lg:flex-row items-center justify-center gap-16 lg:gap-20 px-8 py-8 lg:py-20">
      <div className="flex flex-col gap-10 lg:gap-14 items-center justify-center text-center lg:text-left lg:items-start">
        <div className="flex gap-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" 
          height="40px" viewBox="0 -960 960 960" width="40px" fill="#e3e3e3">
          <path d="m385.67-412 35.66-114.67-94-74h115.34L480-717.33l36.67 116.66h116l-94.34 74 35 114.67L480-483l-94.33 71Zm-143 372v-305.67Q200-391 180-446.33 160-501.67 160-560q0-135.33 92.33-227.67Q344.67-880 480-880q135.33 0 227.67 92.33Q800-695.33 800-560q0 58.33-20 113.67-20 55.33-62.67 100.66V-40L480-119.33 242.67-40ZM480-306.67q106 0 179.67-73.66Q733.33-454 733.33-560q0-106-73.66-179.67Q586-813.33 480-813.33q-106 0-179.67 73.66Q226.67-666 226.67-560q0 106 73.66 179.67Q374-306.67 480-306.67Zm-170.67 171L480-186l170.67 50.33V-291q-38.34 26-82.5 38.5Q524-240 480-240t-88.17-12.5q-44.16-12.5-82.5-38.5v155.33Zm170.67-78Z"/>
          </svg>
          <div className="flex flex-col justify-center">
          <p>Satisfaction Guaranteed</p>
          </div>
        </div>
        <h1 className="font-extrabold text-4xl lg:text-6xl tracking-tight md:-mb-4">
          Cook smarter, not harder
        </h1>
        <p className="text-lg opacity-80 leading-relaxed">
          Rediscover the joy of cooking with intelligent meal planning, automated grocery lists, and a kitchen that finally works for you.
        </p>
        <button className="btn btn-warning btn-wide">
          Get {config.appName}
        </button>

        <TestimonialsAvatars priority={true} />
      </div>
      <div className="lg:w-full animate-appearFromRight">
        <Image
          src="/hero-banner.png"
          alt="Hero Banner for Oven350"
          className="w-full"
          priority={true}
          width={1500}
          height={825}
        />
      </div>
    </section>
  );
};

export default Hero;
