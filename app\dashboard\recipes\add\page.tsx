import AddRecipeForm from "@/components/dashboard/recipes/AddRecipeForm";
import { ArrowLeftIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

export const dynamic = "force-dynamic";

export default async function AddRecipePage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link 
          href="/dashboard/recipes"
          className="btn btn-ghost btn-sm btn-circle"
        >
          <ArrowLeftIcon className="w-4 h-4" />
        </Link>
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold text-base-content">Add New Recipe</h1>
          <p className="text-base-content/70 mt-1">
            Create a new recipe and add it to your collection
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="max-w-4xl">
        <AddRecipeForm />
      </div>
    </div>
  );
}
