# ShipFast — Typescript

Hey maker 👋 it's <PERSON> from [ShipFast](https://shipfa.st/docs). Let's get your startup off the ground, FAST ⚡️

<sub>**Watch/Star the repo to be notified when updates are pushed**</sub>

## Get Started

1. Follow the [Get Started Tutorial](https://shipfa.st/docs) to clone the repo and run your local server 💻

<sub>**Looking for the /pages router version?** Use this [documentation](https://shipfa.st/docs-old) instead</sub>

2. Follow the [Ship In 5 Minutes Tutorial](https://shipfa.st/docs/tutorials/ship-in-5-minutes) to learn the foundation and ship your app quickly ⚡️

## Links

-   [📚 Documentation](https://shipfa.st/docs)
-   [📣 Updates](https://shipfast.beehiiv.com/)
-   [🧑‍💻 Discord](https://shipfa.st/dashboard)
-   [🥇 Leaderboard](https://shipfa.st/leaderboard)

## Support

Reach <NAME_EMAIL>

Let's ship it, FAST ⚡️

\_

**📈 Grow your startup with [DataFast](https://datafa.st?ref=shipfast_readme)**

-   Analyze your traffic
-   Get insights on your customers
-   Make data-driven decisions

ShipFast members get 30% OFF on all plans! 🎁

![datafast](https://github.com/user-attachments/assets/0bf09937-31d1-41d7-82bc-234b5c359a93)
