"use client";

import { useState } from "react";
import { 
  MagnifyingGlassIcon,
  FunnelIcon,
  XMarkIcon
} from "@heroicons/react/24/outline";

const categories = [
  "All Categories",
  "Breakfast",
  "Lunch", 
  "Dinner",
  "Dessert",
  "Snacks",
  "Beverages"
];

const cuisines = [
  "All Cuisines",
  "American",
  "Italian",
  "Mexican",
  "Asian",
  "Mediterranean",
  "Indian",
  "French"
];

const dietaryRestrictions = [
  "Vegetarian",
  "Vegan", 
  "Gluten-Free",
  "Dairy-Free",
  "Keto",
  "Low-Carb",
  "Paleo"
];

const cookTimes = [
  "Any Time",
  "Under 15 min",
  "15-30 min",
  "30-60 min",
  "Over 1 hour"
];

export default function RecipeFilters() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All Categories");
  const [selectedCuisine, setSelectedCuisine] = useState("All Cuisines");
  const [selectedDietary, setSelectedDietary] = useState<string[]>([]);
  const [selectedCookTime, setSelectedCookTime] = useState("Any Time");
  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleDietaryToggle = (dietary: string) => {
    setSelectedDietary(prev => 
      prev.includes(dietary) 
        ? prev.filter(d => d !== dietary)
        : [...prev, dietary]
    );
  };

  const clearAllFilters = () => {
    setSearchQuery("");
    setSelectedCategory("All Categories");
    setSelectedCuisine("All Cuisines");
    setSelectedDietary([]);
    setSelectedCookTime("Any Time");
  };

  const hasActiveFilters = searchQuery || 
    selectedCategory !== "All Categories" || 
    selectedCuisine !== "All Cuisines" ||
    selectedDietary.length > 0 ||
    selectedCookTime !== "Any Time";

  return (
    <div className="bg-base-200 rounded-xl p-6 border border-base-300">
      {/* Search Bar */}
      <div className="relative mb-4">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <MagnifyingGlassIcon className="h-5 w-5 text-base-content/40" />
        </div>
        <input
          type="text"
          placeholder="Search recipes by name, ingredients, or tags..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="input input-bordered w-full pl-10 pr-4 bg-base-100 border-base-300 focus:border-warning"
        />
      </div>

      {/* Quick Filters */}
      <div className="flex flex-wrap gap-3 mb-4">
        <select 
          value={selectedCategory}
          onChange={(e) => setSelectedCategory(e.target.value)}
          className="select select-bordered select-sm bg-base-100 border-base-300 focus:border-warning"
        >
          {categories.map(category => (
            <option key={category} value={category}>{category}</option>
          ))}
        </select>

        <select 
          value={selectedCuisine}
          onChange={(e) => setSelectedCuisine(e.target.value)}
          className="select select-bordered select-sm bg-base-100 border-base-300 focus:border-warning"
        >
          {cuisines.map(cuisine => (
            <option key={cuisine} value={cuisine}>{cuisine}</option>
          ))}
        </select>

        <select 
          value={selectedCookTime}
          onChange={(e) => setSelectedCookTime(e.target.value)}
          className="select select-bordered select-sm bg-base-100 border-base-300 focus:border-warning"
        >
          {cookTimes.map(time => (
            <option key={time} value={time}>{time}</option>
          ))}
        </select>

        <button
          onClick={() => setShowAdvanced(!showAdvanced)}
          className={`btn btn-sm gap-2 ${showAdvanced ? 'btn-warning' : 'btn-outline'}`}
        >
          <FunnelIcon className="w-4 h-4" />
          Advanced
        </button>

        {hasActiveFilters && (
          <button
            onClick={clearAllFilters}
            className="btn btn-ghost btn-sm gap-2 text-error"
          >
            <XMarkIcon className="w-4 h-4" />
            Clear All
          </button>
        )}
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="border-t border-base-300 pt-4">
          <div className="space-y-4">
            {/* Dietary Restrictions */}
            <div>
              <label className="block text-sm font-medium text-base-content mb-2">
                Dietary Restrictions
              </label>
              <div className="flex flex-wrap gap-2">
                {dietaryRestrictions.map(dietary => (
                  <button
                    key={dietary}
                    onClick={() => handleDietaryToggle(dietary)}
                    className={`btn btn-xs ${
                      selectedDietary.includes(dietary) 
                        ? 'btn-warning' 
                        : 'btn-outline'
                    }`}
                  >
                    {dietary}
                  </button>
                ))}
              </div>
            </div>

            {/* Rating Filter */}
            <div>
              <label className="block text-sm font-medium text-base-content mb-2">
                Minimum Rating
              </label>
              <div className="flex gap-2">
                {[1, 2, 3, 4, 5].map(rating => (
                  <button
                    key={rating}
                    className="btn btn-xs btn-outline"
                  >
                    {rating}⭐+
                  </button>
                ))}
              </div>
            </div>

            {/* Difficulty Filter */}
            <div>
              <label className="block text-sm font-medium text-base-content mb-2">
                Difficulty Level
              </label>
              <div className="flex gap-2">
                {["Easy", "Medium", "Hard"].map(difficulty => (
                  <button
                    key={difficulty}
                    className="btn btn-xs btn-outline"
                  >
                    {difficulty}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-base-300">
          <div className="flex flex-wrap gap-2">
            {searchQuery && (
              <span className="badge badge-warning gap-2">
                Search: "{searchQuery}"
                <button onClick={() => setSearchQuery("")}>
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </span>
            )}
            {selectedCategory !== "All Categories" && (
              <span className="badge badge-warning gap-2">
                {selectedCategory}
                <button onClick={() => setSelectedCategory("All Categories")}>
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </span>
            )}
            {selectedCuisine !== "All Cuisines" && (
              <span className="badge badge-warning gap-2">
                {selectedCuisine}
                <button onClick={() => setSelectedCuisine("All Cuisines")}>
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </span>
            )}
            {selectedDietary.map(dietary => (
              <span key={dietary} className="badge badge-warning gap-2">
                {dietary}
                <button onClick={() => handleDietaryToggle(dietary)}>
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </span>
            ))}
            {selectedCookTime !== "Any Time" && (
              <span className="badge badge-warning gap-2">
                {selectedCookTime}
                <button onClick={() => setSelectedCookTime("Any Time")}>
                  <XMarkIcon className="w-3 h-3" />
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
