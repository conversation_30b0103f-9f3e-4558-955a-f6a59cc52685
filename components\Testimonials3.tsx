import Image from "next/image";
import { StaticImageData } from "next/image";
import config from "@/config";

// The list of your testimonials. It needs 3 items to fill the row.
const list: {
  username?: string;
  name: string;
  text: string;
  img?: string | StaticImageData;
}[] = [
  {
    // Optional, use for social media like Twitter. Does not link anywhere but cool to display
    username: "callme_sarah",
    // REQUIRED
    name: "<PERSON>",
    // REQUIRED
    text: "This app saved my weeknights! Before OVEN350, figuring out dinner was my biggest daily stress. Now, I spend 20 minutes on Sunday planning our entire week, and the automated grocery list is a lifesaver – no more forgotten ingredients or extra trips to the store. It has genuinely given me back hours of my week and brought sanity back to our family meals.",
    // Optional, a statically imported image (usually from your public folder—recommended) or a link to the person's avatar. Shows a fallback letter if not provided
    img: "/Person-With-Headphone.png",
  },
  {
    username: "the_mcdavid",
    name: "<PERSON>",
    text: "My Smart Kitchen finally feels smart! I bought a smart oven a year ago, but it always felt like a gimmick. OVEN350 completely changed that. Sending a recipe's exact time and temperature from the app to my oven with one tap feels like living in the future. It’s not just a recipe book; it’s the brain my kitchen was missing. I'm cooking more precisely and trying more ambitious recipes because of it.",
    img: "/David-L.png",
  },
  {
    username: "recipe_queen_elena",
    name: "Elena R",
    text: "My entire culinary world, all in one place! I'm a recipe hoarder. I had them in bookmarks, on Pinterest, and in a dozen notebooks. It was chaos. The OVEN350 web importer is pure magic – I've finally consolidated my entire collection into one beautiful, searchable place. The automatic nutritional information is the cherry on top. I can't imagine my kitchen without it now.",
    img: "/Elena-R.png",
  },
];

// A single testimonial, to be rendered in  a list
const Testimonial = ({ i }: { i: number }) => {
  const testimonial = list[i];

  if (!testimonial) return null;

  return (
    <li key={i}>
      <figure className="relative max-w-lg h-full p-6 md:p-10 bg-base-200 rounded-2xl max-md:text-sm flex flex-col">
        <blockquote className="relative flex-1">
          <p className="text-base-content/80 leading-relaxed">
            {testimonial.text}
          </p>
        </blockquote>
        <figcaption className="relative flex items-center justify-start gap-4 pt-4 mt-4 md:gap-8 md:pt-8 md:mt-8 border-t border-base-content/5">
          <div className="w-full flex items-center justify-between gap-2">
            <div>
              <div className="font-medium text-base-content md:mb-0.5">
                {testimonial.name}
              </div>
              {testimonial.username && (
                <div className="mt-0.5 text-sm text-base-content/80">
                  @{testimonial.username}
                </div>
              )}
            </div>

            <div className="overflow-hidden rounded-full bg-base-300 shrink-0">
              {testimonial.img ? (
                <Image
                  className="w-10 h-10 md:w-12 md:h-12 rounded-full object-cover"
                  src={list[i].img}
                  alt={`${list[i].name}'s testimonial for ${config.appName}`}
                  width={48}
                  height={48}
                />
              ) : (
                <span className="w-10 h-10 md:w-12 md:h-12 rounded-full flex justify-center items-center text-lg font-medium bg-base-300">
                  {testimonial.name.charAt(0)}
                </span>
              )}
            </div>
          </div>
        </figcaption>
      </figure>
    </li>
  );
};

const Testimonials3 = () => {
  return (
    <section id="testimonials">
      <div className="py-24 px-8 max-w-7xl mx-auto">
        <div className="flex flex-col text-center w-full mb-20">
          <div className="mb-8">
            <h2 className="sm:text-5xl text-4xl font-extrabold text-base-content">
              Loved by Home Cooks Everywhere!
            </h2>
          </div>
          <p className="lg:w-2/3 mx-auto leading-relaxed text-base text-base-content/80">
            See what our users are saying about their new, streamlined kitchen experience.
          </p>
        </div>

        <ul
          role="list"
          className="flex flex-col items-center lg:flex-row lg:items-stretch gap-6 lg:gap-8"
        >
          {[...Array(3)].map((e, i) => (
            <Testimonial key={i} i={i} />
          ))}
        </ul>
      </div>
    </section>
  );
};

export default Testimonials3;
