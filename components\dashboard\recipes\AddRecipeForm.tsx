"use client";

import { useState } from "react";
import { 
  PlusIcon, 
  XMarkIcon, 
  PhotoIcon,
  ClockIcon,
  UsersIcon
} from "@heroicons/react/24/outline";
import { Ingredient, Instruction, RecipeFormData } from "@/types/recipe";


const categories = [
  'Breakfast', 'Lunch', 'Dinner', 'Dessert', 'Snack', 'Beverage', 'Appetizer'
];

const cuisines = [
  'American', 'Italian', 'Mexican', 'Asian', 'Mediterranean', 'Indian', 'French', 'Caribbean', 'Other'
];

const dietaryOptions = [
  'vegetarian', 'vegan', 'gluten-free', 'dairy-free', 'nut-free', 'keto', 'paleo', 'low-carb', 'low-fat'
];

const commonUnits = [
  'cup', 'cups', 'tbsp', 'tsp', 'oz', 'lb', 'g', 'kg', 'ml', 'l', 'piece', 'pieces', 'clove', 'cloves'
];

export default function AddRecipeForm() {
  const [formData, setFormData] = useState<RecipeFormData>({
    name: '',
    description: '',
    servings: 4,
    prepTime: 15,
    cookTime: 30,
    difficulty: 'Medium',
    category: 'Dinner',
    cuisine: 'Other',
    ingredients: [{ name: '', amount: 1, unit: 'cup', notes: '' }],
    instructions: [{ stepNumber: 1, instruction: '', duration: 0, temperature: 0 }],
    tags: [],
    dietaryRestrictions: [],
    images: null
  });

  const [currentTag, setCurrentTag] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const addIngredient = () => {
    setFormData(prev => ({
      ...prev,
      ingredients: [...prev.ingredients, { name: '', amount: 1, unit: 'cup', notes: '' }]
    }));
  };

  const removeIngredient = (index: number) => {
    setFormData(prev => ({
      ...prev,
      ingredients: prev.ingredients.filter((_, i) => i !== index)
    }));
  };

  const updateIngredient = (index: number, field: keyof Ingredient, value: any) => {
    setFormData(prev => ({
      ...prev,
      ingredients: prev.ingredients.map((ing, i) => 
        i === index ? { ...ing, [field]: value } : ing
      )
    }));
  };

  const addInstruction = () => {
    setFormData(prev => ({
      ...prev,
      instructions: [...prev.instructions, { 
        stepNumber: prev.instructions.length + 1, 
        instruction: '', 
        duration: 0, 
        temperature: 0 
      }]
    }));
  };

  const removeInstruction = (index: number) => {
    setFormData(prev => ({
      ...prev,
      instructions: prev.instructions.filter((_, i) => i !== index).map((inst, i) => ({
        ...inst,
        stepNumber: i + 1
      }))
    }));
  };

  const updateInstruction = (index: number, field: keyof Instruction, value: any) => {
    setFormData(prev => ({
      ...prev,
      instructions: prev.instructions.map((inst, i) => 
        i === index ? { ...inst, [field]: value } : inst
      )
    }));
  };

  const addTag = () => {
    if (currentTag.trim() && !formData.tags.includes(currentTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, currentTag.trim()]
      }));
      setCurrentTag('');
    }
  };

  const removeTag = (tag: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag)
    }));
  };

  const toggleDietaryRestriction = (restriction: string) => {
    setFormData(prev => ({
      ...prev,
      dietaryRestrictions: prev.dietaryRestrictions.includes(restriction)
        ? prev.dietaryRestrictions.filter(r => r !== restriction)
        : [...prev.dietaryRestrictions, restriction]
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Here you would make an API call to save the recipe
      console.log('Recipe data:', formData);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Redirect to recipes page or show success message
      alert('Recipe saved successfully!');
      
    } catch (error) {
      console.error('Error saving recipe:', error);
      alert('Error saving recipe. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      {/* Basic Information */}
      <div className="bg-base-200 rounded-xl p-6 border border-base-300">
        <h2 className="text-lg font-semibold text-base-content mb-4">Basic Information</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="lg:col-span-2">
            <label className="block text-sm font-medium text-base-content mb-2">
              Recipe Name *
            </label>
            <input
              type="text"
              required
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              className="input input-bordered w-full bg-base-100 border-base-300 focus:border-warning"
              placeholder="Enter recipe name"
            />
          </div>

          <div className="lg:col-span-2">
            <label className="block text-sm font-medium text-base-content mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="textarea textarea-bordered w-full bg-base-100 border-base-300 focus:border-warning h-24"
              placeholder="Describe your recipe..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-base-content mb-2">
              <UsersIcon className="w-4 h-4 inline mr-1" />
              Servings *
            </label>
            <input
              type="number"
              required
              min="1"
              max="100"
              value={formData.servings}
              onChange={(e) => setFormData(prev => ({ ...prev, servings: parseInt(e.target.value) }))}
              className="input input-bordered w-full bg-base-100 border-base-300 focus:border-warning"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-base-content mb-2">
              <ClockIcon className="w-4 h-4 inline mr-1" />
              Prep Time (minutes) *
            </label>
            <input
              type="number"
              required
              min="0"
              value={formData.prepTime}
              onChange={(e) => setFormData(prev => ({ ...prev, prepTime: parseInt(e.target.value) }))}
              className="input input-bordered w-full bg-base-100 border-base-300 focus:border-warning"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-base-content mb-2">
              <ClockIcon className="w-4 h-4 inline mr-1" />
              Cook Time (minutes) *
            </label>
            <input
              type="number"
              required
              min="0"
              value={formData.cookTime}
              onChange={(e) => setFormData(prev => ({ ...prev, cookTime: parseInt(e.target.value) }))}
              className="input input-bordered w-full bg-base-100 border-base-300 focus:border-warning"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-base-content mb-2">
              Difficulty *
            </label>
            <select
              required
              value={formData.difficulty}
              onChange={(e) => setFormData(prev => ({ ...prev, difficulty: e.target.value as any }))}
              className="select select-bordered w-full bg-base-100 border-base-300 focus:border-warning"
            >
              <option value="Easy">Easy</option>
              <option value="Medium">Medium</option>
              <option value="Hard">Hard</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-base-content mb-2">
              Category *
            </label>
            <select
              required
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              className="select select-bordered w-full bg-base-100 border-base-300 focus:border-warning"
            >
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-base-content mb-2">
              Cuisine
            </label>
            <select
              value={formData.cuisine}
              onChange={(e) => setFormData(prev => ({ ...prev, cuisine: e.target.value }))}
              className="select select-bordered w-full bg-base-100 border-base-300 focus:border-warning"
            >
              {cuisines.map(cuisine => (
                <option key={cuisine} value={cuisine}>{cuisine}</option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Ingredients */}
      <div className="bg-base-200 rounded-xl p-6 border border-base-300">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-base-content">Ingredients</h2>
          <button
            type="button"
            onClick={addIngredient}
            className="btn btn-warning btn-sm gap-2"
          >
            <PlusIcon className="w-4 h-4" />
            Add Ingredient
          </button>
        </div>

        <div className="space-y-3">
          {formData.ingredients.map((ingredient, index) => (
            <div key={index} className="grid grid-cols-12 gap-3 items-end">
              <div className="col-span-5">
                <label className="block text-xs font-medium text-base-content/70 mb-1">
                  Ingredient Name
                </label>
                <input
                  type="text"
                  required
                  value={ingredient.name}
                  onChange={(e) => updateIngredient(index, 'name', e.target.value)}
                  className="input input-bordered input-sm w-full bg-base-100 border-base-300 focus:border-warning"
                  placeholder="e.g., Chicken breast"
                />
              </div>

              <div className="col-span-2">
                <label className="block text-xs font-medium text-base-content/70 mb-1">
                  Amount
                </label>
                <input
                  type="number"
                  required
                  min="0"
                  step="0.25"
                  value={ingredient.amount}
                  onChange={(e) => updateIngredient(index, 'amount', parseFloat(e.target.value))}
                  className="input input-bordered input-sm w-full bg-base-100 border-base-300 focus:border-warning"
                />
              </div>

              <div className="col-span-2">
                <label className="block text-xs font-medium text-base-content/70 mb-1">
                  Unit
                </label>
                <select
                  required
                  value={ingredient.unit}
                  onChange={(e) => updateIngredient(index, 'unit', e.target.value)}
                  className="select select-bordered select-sm w-full bg-base-100 border-base-300 focus:border-warning"
                >
                  {commonUnits.map(unit => (
                    <option key={unit} value={unit}>{unit}</option>
                  ))}
                </select>
              </div>

              <div className="col-span-2">
                <label className="block text-xs font-medium text-base-content/70 mb-1">
                  Notes
                </label>
                <input
                  type="text"
                  value={ingredient.notes || ''}
                  onChange={(e) => updateIngredient(index, 'notes', e.target.value)}
                  className="input input-bordered input-sm w-full bg-base-100 border-base-300 focus:border-warning"
                  placeholder="optional"
                />
              </div>

              <div className="col-span-1">
                {formData.ingredients.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeIngredient(index)}
                    className="btn btn-ghost btn-sm btn-circle text-error"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-base-200 rounded-xl p-6 border border-base-300">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-base-content">Instructions</h2>
          <button
            type="button"
            onClick={addInstruction}
            className="btn btn-warning btn-sm gap-2"
          >
            <PlusIcon className="w-4 h-4" />
            Add Step
          </button>
        </div>

        <div className="space-y-4">
          {formData.instructions.map((instruction, index) => (
            <div key={index} className="bg-base-100 rounded-lg p-4 border border-base-300">
              <div className="flex items-center justify-between mb-3">
                <span className="badge badge-warning">Step {instruction.stepNumber}</span>
                {formData.instructions.length > 1 && (
                  <button
                    type="button"
                    onClick={() => removeInstruction(index)}
                    className="btn btn-ghost btn-xs btn-circle text-error"
                  >
                    <XMarkIcon className="w-3 h-3" />
                  </button>
                )}
              </div>

              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-base-content mb-2">
                    Instruction
                  </label>
                  <textarea
                    required
                    value={instruction.instruction}
                    onChange={(e) => updateInstruction(index, 'instruction', e.target.value)}
                    className="textarea textarea-bordered w-full bg-base-100 border-base-300 focus:border-warning h-20"
                    placeholder="Describe this step in detail..."
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-base-content mb-2">
                      Duration (minutes)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={instruction.duration || ''}
                      onChange={(e) => updateInstruction(index, 'duration', parseInt(e.target.value) || 0)}
                      className="input input-bordered input-sm w-full bg-base-100 border-base-300 focus:border-warning"
                      placeholder="Optional"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-base-content mb-2">
                      Temperature (°F)
                    </label>
                    <input
                      type="number"
                      min="0"
                      value={instruction.temperature || ''}
                      onChange={(e) => updateInstruction(index, 'temperature', parseInt(e.target.value) || 0)}
                      className="input input-bordered input-sm w-full bg-base-100 border-base-300 focus:border-warning"
                      placeholder="Optional"
                    />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Tags and Dietary Restrictions */}
      <div className="bg-base-200 rounded-xl p-6 border border-base-300">
        <h2 className="text-lg font-semibold text-base-content mb-4">Tags & Dietary Information</h2>

        <div className="space-y-6">
          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-base-content mb-2">
              Tags
            </label>
            <div className="flex gap-2 mb-3">
              <input
                type="text"
                value={currentTag}
                onChange={(e) => setCurrentTag(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                className="input input-bordered flex-1 bg-base-100 border-base-300 focus:border-warning"
                placeholder="Add a tag and press Enter"
              />
              <button
                type="button"
                onClick={addTag}
                className="btn btn-warning btn-sm"
              >
                Add
              </button>
            </div>

            <div className="flex flex-wrap gap-2">
              {formData.tags.map(tag => (
                <span key={tag} className="badge badge-warning gap-2">
                  {tag}
                  <button
                    type="button"
                    onClick={() => removeTag(tag)}
                    className="btn btn-ghost btn-xs btn-circle"
                  >
                    <XMarkIcon className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
          </div>

          {/* Dietary Restrictions */}
          <div>
            <label className="block text-sm font-medium text-base-content mb-2">
              Dietary Restrictions
            </label>
            <div className="flex flex-wrap gap-2">
              {dietaryOptions.map(option => (
                <button
                  key={option}
                  type="button"
                  onClick={() => toggleDietaryRestriction(option)}
                  className={`btn btn-xs ${
                    formData.dietaryRestrictions.includes(option)
                      ? 'btn-warning'
                      : 'btn-outline'
                  }`}
                >
                  {option}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Recipe Image */}
      <div className="bg-base-200 rounded-xl p-6 border border-base-300">
        <h2 className="text-lg font-semibold text-base-content mb-4">Recipe Image</h2>

        <div className="border-2 border-dashed border-base-300 rounded-lg p-8 text-center">
          <PhotoIcon className="w-12 h-12 mx-auto text-base-content/40 mb-4" />
          <p className="text-base-content/60 mb-4">Upload a photo of your recipe</p>
          <button type="button" className="btn btn-outline btn-sm">
            Choose Image
          </button>
        </div>
      </div>

      {/* Submit Button */}
      <div className="flex justify-end gap-4">
        <button
          type="button"
          className="btn btn-ghost"
          onClick={() => window.history.back()}
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={isSubmitting}
          className="btn btn-warning"
        >
          {isSubmitting ? (
            <>
              <span className="loading loading-spinner loading-sm"></span>
              Saving...
            </>
          ) : (
            'Save Recipe'
          )}
        </button>
      </div>
    </form>
  );
}
