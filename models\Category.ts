import mongoose from "mongoose";
import toJ<PERSON><PERSON> from "./plugins/toJSON";

const categorySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 500,
    },
    
    // Visual
    color: {
      type: String,
      trim: true,
      match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 
      default: '#6B7280',
    },
    icon: {
      type: String,
      trim: true,
    },
    
    // Hierarchy
    parentCategory: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
    },
    subcategories: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
    }],
    level: {
      type: Number,
      min: 0,
      max: 3,
      default: 0,
    },
    
    // User and System
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    isSystem: {
      type: Boolean,
      default: false, 
    },
    isPublic: {
      type: Boolean,
      default: false,
    },
    
    // Usage Statistics
    recipeCount: {
      type: Number,
      min: 0,
      default: 0,
    },
    usageCount: {
      type: Number,
      min: 0,
      default: 0,
    },
    
    // Organization
    sortOrder: {
      type: Number,
      default: 0,
    },
    
    // Status
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

// Indexes
categorySchema.index({ userId: 1, isActive: 1 });
categorySchema.index({ parentCategory: 1 });
categorySchema.index({ isSystem: 1, isPublic: 1 });
categorySchema.index({ name: 'text', description: 'text' });

// Virtual for full path
categorySchema.virtual('fullPath').get(function() {
  // This would need to be populated to work properly
  return this.name;
});

// Method to get all subcategories recursively
categorySchema.methods.getAllSubcategories = async function() {
  const Category = mongoose.model('Category');
  const subcats = await Category.find({ parentCategory: this._id });
  let allSubcats = [...subcats];
  
  for (const subcat of subcats) {
    const nestedSubcats = await subcat.getAllSubcategories();
    allSubcats = allSubcats.concat(nestedSubcats);
  }
  
  return allSubcats;
};

// Add plugin that converts mongoose to json
categorySchema.plugin(toJSON);

export default mongoose.models.Category || mongoose.model("Category", categorySchema);
