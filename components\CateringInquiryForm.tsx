"use client"; // This marks the component for client-side interactivity.

import { useState } from 'react';

// The form component is now self-contained.
const CateringInquiryForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    eventType: '',
    guestCount: '',
    eventDate: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionStatus, setSubmissionStatus] = useState(null); // 'success', 'error', or null

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prevState => ({
      ...prevState,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmissionStatus(null);

    // In a real application, you would send the formData to your backend API here.
    // e.g., await fetch('/api/catering-inquiry', { method: 'POST', body: JSON.stringify(formData) });
    
    // Simulating an API call with a 2-second delay
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // On successful submission:
      setSubmissionStatus('success');
      setFormData({ // Reset form
        name: '', email: '', phone: '', eventType: '', guestCount: '', eventDate: '', message: ''
      });
    } catch (error) {
      // On failed submission:
      setSubmissionStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form 
      onSubmit={handleSubmit}
      className="space-y-4 bg-base-100 p-8 rounded-lg shadow-lg"
    >
      <div>
        <label htmlFor="name" className="label"><span className="label-text">Full Name</span></label>
        <input type="text" id="name" name="name" value={formData.name} onChange={handleChange} placeholder="John Doe" className="input input-bordered w-full" required />
      </div>
      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="email" className="label"><span className="label-text">Email Address</span></label>
          <input type="email" id="email" name="email" value={formData.email} onChange={handleChange} placeholder="<EMAIL>" className="input input-bordered w-full" required />
        </div>
        <div>
          <label htmlFor="phone" className="label"><span className="label-text">Phone Number</span></label>
          <input type="tel" id="phone" name="phone" value={formData.phone} onChange={handleChange} placeholder="(*************" className="input input-bordered w-full" />
        </div>
      </div>
      <div className="grid md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="eventType" className="label"><span className="label-text">Type of Event</span></label>
          <select id="eventType" name="eventType" value={formData.eventType} onChange={handleChange} className="select select-bordered w-full" required>
            <option value="" disabled>Select an event</option>
            <option>Wedding</option>
            <option>Birthday Party</option>
            <option>Communion / Christening</option>
            <option>Baby / Bridal Shower</option>
            <option>Corporate Event</option>
            <option>Other Special Event</option>
          </select>
        </div>
        <div>
          <label htmlFor="guestCount" className="label"><span className="label-text">Estimated Guest Count</span></label>
          <input type="number" id="guestCount" name="guestCount" value={formData.guestCount} onChange={handleChange} placeholder="50" className="input input-bordered w-full" min="10" />
        </div>
      </div>
       <div>
          <label htmlFor="eventDate" className="label"><span className="label-text">Date of Event</span></label>
          <input type="date" id="eventDate" name="eventDate" value={formData.eventDate} onChange={handleChange} className="input input-bordered w-full" />
        </div>
      <div>
        <label htmlFor="message" className="label"><span className="label-text">Tell us about your event</span></label>
        <textarea id="message" name="message" value={formData.message} onChange={handleChange} className="textarea textarea-bordered w-full" placeholder="Please tell us a bit about your vision for the event, location (home or venue), and any special requests." rows={4}></textarea>
      </div>
      <button type="submit" className="btn btn-warning w-full" disabled={isSubmitting}>
        {isSubmitting ? <span className="loading loading-spinner"></span> : 'Request Catering Information'}
      </button>

      {submissionStatus === 'success' && (
        <div role="alert" className="alert alert-success mt-4">
          <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
          <span>Thank you! Your inquiry has been sent. We'll get back to you within 24-48 hours.</span>
        </div>
      )}
      {submissionStatus === 'error' && (
         <div role="alert" className="alert alert-error mt-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <span>Submission failed. Please try again or email us directly.</span>
        </div>
      )}
    </form>
  );
};

export default CateringInquiryForm;
