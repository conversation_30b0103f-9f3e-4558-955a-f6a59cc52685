// This is now a Server Component by default (no "use client" directive).

import Link from "next/link";
import { getSEOTags } from "@/libs/seo";
import Image from "next/image";
import config from "@/config";
import Footer from "@/components/Footer";
import CateringInquiryForm from "@/components/CateringInquiryForm"; // Import the Client Component

// The metadata export now works correctly because this is a Server Component.
export const metadata = getSEOTags({
  title: `Caribbean Catering Services | ${config.appName}`,
  canonicalUrlRelative: '/catering',
  description: "Bring the vibrant flavors of the Caribbean to your special event. OVEN350 offers full-service catering for weddings, corporate events, and parties in NJ, PA, and NY.",
});


// The Main Catering Page Component
const Catering = () => {

  return (
    <>
      <main className="max-w-7xl mx-auto">
        <div className="p-5 md:p-8">
          <Link href="/" className="btn btn-ghost mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5">
              <path fillRule="evenodd" d="M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z" clipRule="evenodd" />
            </svg>
            Back
          </Link>
          
          {/* Hero Section */}
          <div className="text-center my-8 md:my-12">
            <h1 className="text-4xl md:text-6xl font-extrabold pb-4 text-warning">
              A Taste of the Caribbean, For Your Special Occasion
            </h1>
            <p className="text-lg text-base-content/80 max-w-3xl mx-auto">
              From intimate family gatherings to grand corporate events, {config.appName} Catering brings vibrant, authentic Caribbean cuisine to you. Let us make your next event unforgettable.
            </p>
          </div>

          <div className="space-y-16 md:space-y-24">
            
            {/* Events Section */}
            <section className="text-center">
              <h2 className="text-3xl font-bold mb-8">Catering for Every Celebration</h2>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 text-center">
                  {['Weddings', 'Birthdays', 'Communions', 'Showers', 'Christenings', 'Corporate'].map(event => (
                      <div key={event} className="bg-base-200/50 p-4 rounded-lg font-semibold">{event}</div>
                  ))}
              </div>
            </section>

            {/* Service Styles Section */}
            <section className="grid md:grid-cols-3 gap-8 text-center">
              <div className="bg-base-100 p-6 rounded-lg shadow-md">
                <h3 className="text-2xl font-bold mb-2">Full-Service Catering</h3>
                <p>We handle everything from setup to cleanup at your home or chosen venue. Enjoy your event while we manage the culinary experience.</p>
              </div>
              <div className="bg-base-100 p-6 rounded-lg shadow-md">
                <h3 className="text-2xl font-bold mb-2">Venue Catering</h3>
                <p>Collaborating with your event space, we provide exceptional food service that complements your celebration perfectly.</p>
              </div>
              <div className="bg-base-100 p-6 rounded-lg shadow-md">
                <h3 className="text-2xl font-bold mb-2">Food Delivery</h3>
                <p>Prefer to handle the service yourself? We can prepare our delicious dishes and deliver them hot and ready to serve at your location.</p>
              </div>
            </section>

            {/* Inquiry & Consultation Section */}
            <section className="grid lg:grid-cols-2 gap-12 items-center bg-base-200/50 p-8 rounded-2xl">
              <div>
                <h2 className="text-3xl font-bold mb-4">Your Vision, Our Expertise</h2>
                <div className="prose prose-lg max-w-none">
                  <p>Every event is unique. That's why we begin with a thorough consultation to understand your vision, tastes, and needs. We don't believe in a one-size-fits-all approach.</p>
                  <p>Based on our conversation, we'll recommend the perfect package and craft a menu that will delight you and your guests. It all starts with a conversation.</p>
                  <p><strong>Ready to start planning?</strong> Fill out the form, or email us directly at <a href="mailto:<EMAIL>" className="link link-warning"><EMAIL></a>.</p>
                </div>
              </div>
              {/* The interactive form is now cleanly imported and used here */}
              <CateringInquiryForm />
            </section>

            {/* Service Area & Map Section */}
            <section>
                <h2 className="text-3xl font-bold text-center mb-8">Proudly Serving the Tri-State Area</h2>
                <div className="text-center max-w-4xl mx-auto mb-8 grid grid-cols-1 md:grid-cols-3 gap-4 text-lg">
                    <div className="font-bold">New Jersey <span className="font-normal block text-sm">(North, Central, South)</span></div>
                    <div className="font-bold">Pennsylvania <span className="font-normal block text-sm">(Philadelphia Metro)</span></div>
                    <div className="font-bold">New York <span className="font-normal block text-sm">(Manhattan, Brooklyn, Queens, Long Island)</span></div>
                </div>
                <div className="w-full h-96 rounded-lg overflow-hidden shadow-lg border border-base-300">
                    <iframe
                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d195884.2255787688!2d-75.02107772186938!3d40.00497217436034!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89c134015f62502b%3A0x9b334341339f485!2sBurlington%20County%2C%20NJ!5e0!3m2!1sen!2sus!4v1687000000000!5m2!1sen!2sus"
                        width="100%"
                        height="100%"
                        style={{ border: 0 }}
                        allowFullScreen=""
                        loading="lazy"
                        referrerPolicy="no-referrer-when-downgrade"
                        title="OVEN350 Service Area Map"
                    ></iframe>
                </div>
            </section>

          </div>
        </div>
      </main>
      <Footer />
    </>
  );

};

export default Catering;
