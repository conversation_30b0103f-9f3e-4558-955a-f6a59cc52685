import { Suspense } from "react";
import RecipeGrid from "@/components/dashboard/recipes/RecipeGrid";
import RecipeFilters from "@/components/dashboard/recipes/RecipeFilters";
import { PlusIcon } from "@heroicons/react/24/outline";
import Link from "next/link";

export const dynamic = "force-dynamic";

export default async function RecipesPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold text-base-content">My Recipes</h1>
          <p className="text-base-content/70 mt-1">
            Manage and organize your recipe collection
          </p>
        </div>
        <div className="flex gap-3">
          <Link 
            href="/dashboard/recipes/import"
            className="btn btn-outline btn-sm"
          >
            Import Recipe
          </Link>
          <Link 
            href="/dashboard/recipes/add"
            className="btn btn-warning btn-sm gap-2"
          >
            <PlusIcon className="w-4 h-4" />
            Add Recipe
          </Link>
        </div>
      </div>

      {/* Filters */}
      <RecipeFilters />

      {/* Recipe Grid */}
      <Suspense fallback={
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="bg-base-200 rounded-xl p-4 animate-pulse">
              <div className="aspect-video bg-base-300 rounded-lg mb-4"></div>
              <div className="h-4 bg-base-300 rounded mb-2"></div>
              <div className="h-3 bg-base-300 rounded w-2/3"></div>
            </div>
          ))}
        </div>
      }>
        <RecipeGrid />
      </Suspense>
    </div>
  );
}
