"use client";

import { useState } from "react";
import { 
  ChevronLeftIcon,
  ChevronRightIcon,
  PlusIcon,
  XMarkIcon,
  ShoppingCartIcon
} from "@heroicons/react/24/outline";

interface MealSlot {
  id: string;
  recipe?: {
    id: number;
    name: string;
    image: string;
    cookTime: string;
    servings?: number;
  };
}

interface DayPlan {
  date: Date;
  breakfast: MealSlot[];
  lunch: MealSlot[];
  dinner: MealSlot[];
  snacks: MealSlot[];
}

const mealTypes = ['breakfast', 'lunch', 'dinner', 'snacks'] as const;
type MealType = typeof mealTypes[number];

export default function MealPlannerCalendar() {
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [weekPlan, setWeekPlan] = useState<DayPlan[]>([]);

  // Generate week dates
  const getWeekDates = (date: Date) => {
    const week = [];
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay()); // Start from Sunday
    
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      week.push(day);
    }
    return week;
  };

  const weekDates = getWeekDates(currentWeek);
  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  // Initialize week plan if empty
  if (weekPlan.length === 0) {
    const initialPlan = weekDates.map(date => ({
      date,
      breakfast: [{ id: `${date.toISOString()}-breakfast-1` }],
      lunch: [{ id: `${date.toISOString()}-lunch-1` }],
      dinner: [{ id: `${date.toISOString()}-dinner-1` }],
      snacks: [{ id: `${date.toISOString()}-snacks-1` }],
    }));
    setWeekPlan(initialPlan);
  }

  const navigateWeek = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentWeek);
    newDate.setDate(currentWeek.getDate() + (direction === 'next' ? 7 : -7));
    setCurrentWeek(newDate);
    setWeekPlan([]); // Reset plan for new week
  };

  const handleDrop = (e: React.DragEvent, dayIndex: number, mealType: MealType, slotIndex: number) => {
    e.preventDefault();
    
    try {
      const recipeData = JSON.parse(e.dataTransfer.getData("application/json"));
      
      setWeekPlan(prev => {
        const newPlan = [...prev];
        if (newPlan[dayIndex]) {
          newPlan[dayIndex][mealType][slotIndex] = {
            id: `${newPlan[dayIndex].date.toISOString()}-${mealType}-${slotIndex}`,
            recipe: {
              id: recipeData.id,
              name: recipeData.name,
              image: recipeData.image,
              cookTime: recipeData.cookTime,
              servings: 4 // Default servings
            }
          };
        }
        return newPlan;
      });
    } catch (error) {
      console.error('Error parsing dropped data:', error);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = "copy";
  };

  const removeMeal = (dayIndex: number, mealType: MealType, slotIndex: number) => {
    setWeekPlan(prev => {
      const newPlan = [...prev];
      if (newPlan[dayIndex]) {
        newPlan[dayIndex][mealType][slotIndex] = {
          id: `${newPlan[dayIndex].date.toISOString()}-${mealType}-${slotIndex}`
        };
      }
      return newPlan;
    });
  };

  const addMealSlot = (dayIndex: number, mealType: MealType) => {
    setWeekPlan(prev => {
      const newPlan = [...prev];
      if (newPlan[dayIndex]) {
        const newSlotIndex = newPlan[dayIndex][mealType].length;
        newPlan[dayIndex][mealType].push({
          id: `${newPlan[dayIndex].date.toISOString()}-${mealType}-${newSlotIndex}`
        });
      }
      return newPlan;
    });
  };

  const generateGroceryList = () => {
    // This would collect all ingredients from planned meals
    alert('Grocery list generation coming soon!');
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  };

  const isToday = (date: Date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  return (
    <div className="bg-base-200 rounded-xl border border-base-300">
      {/* Header */}
      <div className="p-4 border-b border-base-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h2 className="text-lg font-semibold text-base-content">Weekly Meal Plan</h2>
            <div className="flex items-center gap-2">
              <button
                onClick={() => navigateWeek('prev')}
                className="btn btn-ghost btn-sm btn-circle"
              >
                <ChevronLeftIcon className="w-4 h-4" />
              </button>
              <span className="text-sm font-medium text-base-content min-w-32 text-center">
                {formatDate(weekDates[0])} - {formatDate(weekDates[6])}
              </span>
              <button
                onClick={() => navigateWeek('next')}
                className="btn btn-ghost btn-sm btn-circle"
              >
                <ChevronRightIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          <button
            onClick={generateGroceryList}
            className="btn btn-warning btn-sm gap-2"
          >
            <ShoppingCartIcon className="w-4 h-4" />
            Generate Grocery List
          </button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="p-4">
        <div className="grid grid-cols-8 gap-3">
          {/* Meal Type Labels */}
          <div className="space-y-3">
            <div className="h-12"></div> {/* Space for day headers */}
            {mealTypes.map(mealType => (
              <div key={mealType} className="h-32 flex items-center">
                <span className="text-sm font-medium text-base-content capitalize">
                  {mealType}
                </span>
              </div>
            ))}
          </div>

          {/* Days */}
          {weekDates.map((date, dayIndex) => (
            <div key={date.toISOString()} className="space-y-3">
              {/* Day Header */}
              <div className={`h-12 flex flex-col items-center justify-center rounded-lg border ${
                isToday(date) 
                  ? 'bg-warning text-warning-content border-warning' 
                  : 'bg-base-100 border-base-300'
              }`}>
                <span className="text-xs font-medium">{dayNames[date.getDay()]}</span>
                <span className="text-sm font-bold">{date.getDate()}</span>
              </div>

              {/* Meal Slots */}
              {mealTypes.map(mealType => (
                <div key={mealType} className="space-y-2">
                  {weekPlan[dayIndex]?.[mealType]?.map((slot, slotIndex) => (
                    <div
                      key={slot.id}
                      onDrop={(e) => handleDrop(e, dayIndex, mealType, slotIndex)}
                      onDragOver={handleDragOver}
                      className={`h-28 rounded-lg border-2 border-dashed transition-colors ${
                        slot.recipe 
                          ? 'border-warning bg-warning/5' 
                          : 'border-base-300 hover:border-warning/50 hover:bg-warning/5'
                      }`}
                    >
                      {slot.recipe ? (
                        <div className="h-full p-2 bg-base-100 rounded-lg border border-base-300 relative group">
                          <button
                            onClick={() => removeMeal(dayIndex, mealType, slotIndex)}
                            className="absolute top-1 right-1 btn btn-ghost btn-xs btn-circle opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <XMarkIcon className="w-3 h-3" />
                          </button>
                          
                          <div className="flex gap-2 h-full">
                            <div className="w-8 h-8 bg-base-300 rounded overflow-hidden flex-shrink-0">
                              <img 
                                src={slot.recipe.image} 
                                alt={slot.recipe.name}
                                className="w-full h-full object-cover"
                              />
                            </div>
                            
                            <div className="flex-1 min-w-0">
                              <h4 className="text-xs font-medium text-base-content line-clamp-2 mb-1">
                                {slot.recipe.name}
                              </h4>
                              <div className="text-xs text-base-content/60">
                                {slot.recipe.cookTime}
                              </div>
                              <div className="text-xs text-base-content/60">
                                {slot.recipe.servings} servings
                              </div>
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="h-full flex items-center justify-center text-base-content/40">
                          <span className="text-xs">Drop recipe here</span>
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {/* Add Meal Button */}
                  <button
                    onClick={() => addMealSlot(dayIndex, mealType)}
                    className="w-full h-6 border border-dashed border-base-300 rounded flex items-center justify-center hover:border-warning/50 hover:bg-warning/5 transition-colors"
                  >
                    <PlusIcon className="w-3 h-3 text-base-content/40" />
                  </button>
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
