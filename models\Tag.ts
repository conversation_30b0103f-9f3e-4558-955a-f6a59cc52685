import mongoose from "mongoose";
import to<PERSON><PERSON><PERSON> from "./plugins/toJSON";

// Tag schema for flexible recipe labeling
const tagSchema = new mongoose.Schema(
  {
    // Basic Information
    name: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
      maxlength: 50,
    },
    displayName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 200,
    },
    
    // Visual
    color: {
      type: String,
      trim: true,
      match: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, // Hex color validation
      default: '#6B7280',
    },
    
    // Classification
    category: {
      type: String,
      enum: [
        'cooking-method',    // grilled, baked, fried, etc.
        'dietary',          // vegetarian, vegan, keto, etc.
        'cuisine',          // italian, mexican, asian, etc.
        'meal-type',        // breakfast, lunch, dinner, etc.
        'occasion',         // holiday, party, date-night, etc.
        'difficulty',       // easy, quick, advanced, etc.
        'ingredient',       // chicken, chocolate, pasta, etc.
        'equipment',        // slow-cooker, air-fryer, grill, etc.
        'season',           // summer, winter, spring, fall
        'mood',             // comfort-food, healthy, indulgent, etc.
        'time',             // quick, make-ahead, slow, etc.
        'custom'            // user-defined tags
      ],
      default: 'custom',
    },
    
    // User and System
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    isSystem: {
      type: Boolean,
      default: false, // System tags are predefined
    },
    isPublic: {
      type: Boolean,
      default: false,
    },
    
    // Usage Statistics
    recipeCount: {
      type: Number,
      min: 0,
      default: 0,
    },
    usageCount: {
      type: Number,
      min: 0,
      default: 0,
    },
    popularityScore: {
      type: Number,
      min: 0,
      default: 0,
    },
    
    // Relationships
    synonyms: [{
      type: String,
      trim: true,
      lowercase: true,
    }],
    relatedTags: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Tag',
    }],
    
    // Auto-suggestion
    suggestedFor: [{
      type: String,
      enum: ['beginner', 'intermediate', 'advanced', 'family', 'single', 'couple'],
    }],
    
    // Status
    isActive: {
      type: Boolean,
      default: true,
    },
    isApproved: {
      type: Boolean,
      default: true, // For user-generated tags that need approval
    },
    
    // Trending
    trendingScore: {
      type: Number,
      min: 0,
      default: 0,
    },
    lastTrendingUpdate: {
      type: Date,
      default: Date.now,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

// Indexes
tagSchema.index({ name: 1, userId: 1 }, { unique: true });
tagSchema.index({ category: 1, isActive: 1 });
tagSchema.index({ isSystem: 1, isPublic: 1 });
tagSchema.index({ popularityScore: -1 });
tagSchema.index({ trendingScore: -1 });
tagSchema.index({ name: 'text', displayName: 'text', description: 'text' });

// Virtual for formatted display
tagSchema.virtual('formattedName').get(function() {
  return this.displayName || this.name.charAt(0).toUpperCase() + this.name.slice(1);
});

// Method to increment usage
tagSchema.methods.incrementUsage = function() {
  this.usageCount += 1;
  this.recipeCount += 1;
  this.popularityScore = this.calculatePopularityScore();
};

// Method to decrement usage
tagSchema.methods.decrementUsage = function() {
  this.usageCount = Math.max(0, this.usageCount - 1);
  this.recipeCount = Math.max(0, this.recipeCount - 1);
  this.popularityScore = this.calculatePopularityScore();
};

// Method to calculate popularity score
tagSchema.methods.calculatePopularityScore = function() {
  const recencyWeight = 0.3;
  const usageWeight = 0.7;
  
  // Calculate recency score (newer tags get higher score)
  const daysSinceCreation = (Date.now() - this.createdAt.getTime()) / (1000 * 60 * 60 * 24);
  const recencyScore = Math.max(0, 100 - daysSinceCreation * 0.1);
  
  // Calculate usage score
  const usageScore = Math.min(100, this.usageCount * 2);
  
  return Math.round(recencyScore * recencyWeight + usageScore * usageWeight);
};

// Method to update trending score
tagSchema.methods.updateTrendingScore = function() {
  const now = new Date();
  const daysSinceLastUpdate = (now.getTime() - this.lastTrendingUpdate.getTime()) / (1000 * 60 * 60 * 24);
  
  if (daysSinceLastUpdate >= 1) {
    // Calculate trending based on recent usage
    const recentUsageWeight = 0.6;
    const popularityWeight = 0.4;
    
    // This would need to be calculated based on recent usage data
    const recentUsage = this.usageCount; // Simplified - would need actual recent usage tracking
    const recentUsageScore = Math.min(100, recentUsage * 5);
    
    this.trendingScore = Math.round(
      recentUsageScore * recentUsageWeight + 
      this.popularityScore * popularityWeight
    );
    
    this.lastTrendingUpdate = now;
  }
};

// Static method to get trending tags
tagSchema.statics.getTrendingTags = function(limit = 10) {
  return this.find({ isActive: true, isApproved: true })
    .sort({ trendingScore: -1 })
    .limit(limit);
};

// Static method to get popular tags by category
tagSchema.statics.getPopularByCategory = function(category: string, limit = 20) {
  return this.find({ category, isActive: true, isApproved: true })
    .sort({ popularityScore: -1 })
    .limit(limit);
};

// Static method to search tags
tagSchema.statics.searchTags = function(query: string, limit = 10) {
  return this.find({
    $and: [
      { isActive: true, isApproved: true },
      {
        $or: [
          { name: { $regex: query, $options: 'i' } },
          { displayName: { $regex: query, $options: 'i' } },
          { synonyms: { $in: [new RegExp(query, 'i')] } }
        ]
      }
    ]
  })
  .sort({ popularityScore: -1 })
  .limit(limit);
};

// Pre-save middleware
tagSchema.pre('save', function(next) {
  if (this.isModified('usageCount') || this.isModified('recipeCount')) {
    this.popularityScore = this.calculatePopularityScore();
  }
  next();
});

// Add plugin that converts mongoose to json
tagSchema.plugin(toJSON);

export default mongoose.models.Tag || mongoose.model("Tag", tagSchema);
