"use client";

import Link from "next/link";
import { 
  ClockIcon,
  HeartIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ShareIcon
} from "@heroicons/react/24/outline";
import { HeartIcon as HeartIconSolid } from "@heroicons/react/24/solid";
import { useState } from "react";

// Mock data - this will be replaced with real data from the database
const mockRecipes = [
  {
    id: 1,
    name: "Honey Garlic Chicken",
    description: "Tender chicken glazed with a sweet and savory honey garlic sauce",
    image: "/Chicken.png",
    cookTime: "30 min",
    prepTime: "15 min",
    servings: 4,
    difficulty: "Easy",
    rating: 4.8,
    totalRatings: 24,
    category: "Dinner",
    cuisine: "Asian",
    tags: ["Quick", "Family-Friendly", "Gluten-Free"],
    isFavorite: true,
    createdAt: "2024-01-15",
    lastMade: "2024-01-20"
  },
  {
    id: 2,
    name: "Chocolate Banana Bread",
    description: "Moist and rich banana bread with chocolate chips",
    image: "/Banana-Bread.png",
    cookTime: "1h 15min",
    prepTime: "20 min",
    servings: 8,
    difficulty: "Medium",
    rating: 4.9,
    totalRatings: 18,
    category: "Dessert",
    cuisine: "American",
    tags: ["Baking", "Sweet", "Comfort Food"],
    isFavorite: false,
    createdAt: "2024-01-10",
    lastMade: null
  },
  {
    id: 3,
    name: "Fresh Garden Salad",
    description: "Crisp mixed greens with seasonal vegetables and vinaigrette",
    image: "/Salad.png",
    cookTime: "15 min",
    prepTime: "15 min",
    servings: 2,
    difficulty: "Easy",
    rating: 4.6,
    totalRatings: 12,
    category: "Lunch",
    cuisine: "Mediterranean",
    tags: ["Healthy", "Vegetarian", "Quick"],
    isFavorite: true,
    createdAt: "2024-01-08",
    lastMade: "2024-01-22"
  },
  {
    id: 4,
    name: "Spicy Jerk Chicken",
    description: "Caribbean-style grilled chicken with bold spices",
    image: "/Jerk-Chicken.png",
    cookTime: "45 min",
    prepTime: "30 min",
    servings: 6,
    difficulty: "Medium",
    rating: 4.7,
    totalRatings: 15,
    category: "Dinner",
    cuisine: "Caribbean",
    tags: ["Spicy", "Grilled", "Marinated"],
    isFavorite: false,
    createdAt: "2024-01-05",
    lastMade: "2024-01-18"
  },
  {
    id: 5,
    name: "Classic Margherita Pizza",
    description: "Traditional Italian pizza with fresh mozzarella and basil",
    image: "/Pizza.png",
    cookTime: "25 min",
    prepTime: "2h 30min",
    servings: 4,
    difficulty: "Hard",
    rating: 4.9,
    totalRatings: 32,
    category: "Dinner",
    cuisine: "Italian",
    tags: ["Pizza", "Homemade", "Traditional"],
    isFavorite: true,
    createdAt: "2024-01-03",
    lastMade: "2024-01-21"
  },
  {
    id: 6,
    name: "Fluffy Pancakes",
    description: "Light and airy pancakes perfect for weekend mornings",
    image: "/Waffles.png",
    cookTime: "20 min",
    prepTime: "10 min",
    servings: 4,
    difficulty: "Easy",
    rating: 4.5,
    totalRatings: 28,
    category: "Breakfast",
    cuisine: "American",
    tags: ["Breakfast", "Fluffy", "Weekend"],
    isFavorite: false,
    createdAt: "2024-01-01",
    lastMade: "2024-01-19"
  }
];

export default function RecipeGrid() {
  const [recipes, setRecipes] = useState(mockRecipes);

  const toggleFavorite = (recipeId: number) => {
    setRecipes(prev => prev.map(recipe => 
      recipe.id === recipeId 
        ? { ...recipe, isFavorite: !recipe.isFavorite }
        : recipe
    ));
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "Easy": return "badge-success";
      case "Medium": return "badge-warning";
      case "Hard": return "badge-error";
      default: return "badge-neutral";
    }
  };

  if (recipes.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-24 h-24 mx-auto mb-4 bg-base-300 rounded-full flex items-center justify-center">
          <ClockIcon className="w-12 h-12 text-base-content/40" />
        </div>
        <h3 className="text-lg font-medium text-base-content mb-2">No recipes found</h3>
        <p className="text-base-content/60 mb-6">
          Start building your recipe collection by adding your first recipe.
        </p>
        <Link href="/dashboard/recipes/add" className="btn btn-warning">
          Add Your First Recipe
        </Link>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {recipes.map((recipe) => (
        <div key={recipe.id} className="bg-base-200 rounded-xl overflow-hidden border border-base-300 hover:shadow-lg transition-all duration-200 group">
          {/* Recipe Image */}
          <div className="relative aspect-video bg-base-300">
            <img 
              src={recipe.image} 
              alt={recipe.name}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
            
            {/* Overlay Actions */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-200">
              <div className="absolute top-3 right-3 flex gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                <button
                  onClick={() => toggleFavorite(recipe.id)}
                  className="btn btn-circle btn-sm bg-base-100/90 hover:bg-base-100 border-none"
                >
                  {recipe.isFavorite ? (
                    <HeartIconSolid className="w-4 h-4 text-error" />
                  ) : (
                    <HeartIcon className="w-4 h-4" />
                  )}
                </button>
                <button className="btn btn-circle btn-sm bg-base-100/90 hover:bg-base-100 border-none">
                  <ShareIcon className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Difficulty Badge */}
            <div className="absolute top-3 left-3">
              <span className={`badge badge-sm ${getDifficultyColor(recipe.difficulty)}`}>
                {recipe.difficulty}
              </span>
            </div>
          </div>

          {/* Recipe Content */}
          <div className="p-4">
            <div className="flex items-start justify-between mb-2">
              <h3 className="font-semibold text-base-content line-clamp-1 flex-1">
                {recipe.name}
              </h3>
              <div className="dropdown dropdown-end">
                <label tabIndex={0} className="btn btn-ghost btn-xs">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                  </svg>
                </label>
                <ul tabIndex={0} className="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 rounded-box w-40 border border-base-300">
                  <li>
                    <Link href={`/dashboard/recipes/${recipe.id}`} className="text-xs">
                      <EyeIcon className="w-3 h-3" />
                      View
                    </Link>
                  </li>
                  <li>
                    <Link href={`/dashboard/recipes/${recipe.id}/edit`} className="text-xs">
                      <PencilIcon className="w-3 h-3" />
                      Edit
                    </Link>
                  </li>
                  <li>
                    <button className="text-xs text-error">
                      <TrashIcon className="w-3 h-3" />
                      Delete
                    </button>
                  </li>
                </ul>
              </div>
            </div>

            <p className="text-sm text-base-content/70 line-clamp-2 mb-3">
              {recipe.description}
            </p>

            {/* Recipe Meta */}
            <div className="flex items-center justify-between text-xs text-base-content/60 mb-3">
              <div className="flex items-center gap-1">
                <ClockIcon className="w-3 h-3" />
                <span>{recipe.cookTime}</span>
              </div>
              <div className="flex items-center gap-1">
                <span>👥</span>
                <span>{recipe.servings} servings</span>
              </div>
              <div className="flex items-center gap-1">
                <span>⭐</span>
                <span>{recipe.rating}</span>
                <span>({recipe.totalRatings})</span>
              </div>
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-1 mb-3">
              {recipe.tags.slice(0, 2).map(tag => (
                <span key={tag} className="badge badge-xs badge-outline p-2">
                  {tag}
                </span>
              ))}
              {recipe.tags.length > 2 && (
                <span className="badge badge-xs badge-ghost">
                  +{recipe.tags.length - 2}
                </span>
              )}
            </div>

            {/* Action Button */}
            <Link 
              href={`/dashboard/recipes/${recipe.id}`}
              className="btn btn-warning btn-sm w-full"
            >
              View Recipe
            </Link>
          </div>
        </div>
      ))}
    </div>
  );
}
