"use client";

import Link from "next/link";
import { 
  BookOpenIcon, 
  CalendarDaysIcon, 
  ShoppingCartIcon,
  PlusIcon,
  ClockIcon,
  HeartIcon,
  ChartBarIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline";

const stats = [
  {
    name: "Total Recipes",
    value: "24",
    change: "+3 this week",
    changeType: "positive",
    icon: BookOpenIcon,
    href: "/dashboard/recipes"
  },
  {
    name: "Planned Meals",
    value: "7",
    change: "This week",
    changeType: "neutral",
    icon: CalendarDaysIcon,
    href: "/dashboard/meal-planner"
  },
  {
    name: "Grocery Items",
    value: "18",
    change: "Ready to shop",
    changeType: "neutral",
    icon: ShoppingCartIcon,
    href: "/dashboard/grocery-lists"
  },
  {
    name: "Favorites",
    value: "12",
    change: "+2 this month",
    changeType: "positive",
    icon: HeartIcon,
    href: "/dashboard/favorites"
  }
];

const recentRecipes = [
  {
    id: 1,
    name: "Honey Garlic Chicken",
    image: "/Chicken.png",
    cookTime: "30 min",
    difficulty: "Easy",
    rating: 4.8
  },
  {
    id: 2,
    name: "Chocolate Banana Bread",
    image: "/Banana-Bread.png",
    cookTime: "1h 15min",
    difficulty: "Medium",
    rating: 4.9
  },
  {
    id: 3,
    name: "Fresh Garden Salad",
    image: "/Salad.png",
    cookTime: "15 min",
    difficulty: "Easy",
    rating: 4.6
  }
];

const quickActions = [
  {
    name: "Add New Recipe",
    description: "Create a recipe from scratch",
    icon: PlusIcon,
    href: "/dashboard/recipes/add",
    color: "bg-success text-success-content"
  },
  {
    name: "Import Recipe",
    description: "Import from a website URL",
    icon: ArrowRightIcon,
    href: "/dashboard/recipes/import",
    color: "bg-info text-info-content"
  },
  {
    name: "Plan This Week",
    description: "Set up your weekly meal plan",
    icon: CalendarDaysIcon,
    href: "/dashboard/meal-planner",
    color: "bg-warning text-warning-content"
  },
  {
    name: "View Analytics",
    description: "See your cooking insights",
    icon: ChartBarIcon,
    href: "/dashboard/analytics",
    color: "bg-secondary text-secondary-content"
  }
];

export default function DashboardOverview() {
  return (
    <div className="space-y-8">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Link
              key={stat.name}
              href={stat.href}
              className="bg-base-200 rounded-xl p-6 hover:bg-base-300 transition-colors border border-base-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-base-content/60">{stat.name}</p>
                  <p className="text-2xl font-bold text-base-content">{stat.value}</p>
                  <p className={`text-xs ${
                    stat.changeType === 'positive' ? 'text-success' : 'text-base-content/50'
                  }`}>
                    {stat.change}
                  </p>
                </div>
                <div className="p-3 bg-warning/10 rounded-lg">
                  <Icon className="w-6 h-6 text-warning" />
                </div>
              </div>
            </Link>
          );
        })}
      </div>

      {/* Quick Actions */}
      <div className="bg-base-200 rounded-xl p-6 border border-base-300">
        <h2 className="text-lg font-semibold text-base-content mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action) => {
            const Icon = action.icon;
            return (
              <Link
                key={action.name}
                href={action.href}
                className="flex items-center gap-3 p-4 bg-base-100 rounded-lg hover:bg-base-300 transition-colors border border-base-300"
              >
                <div className={`p-2 rounded-lg ${action.color}`}>
                  <Icon className="w-5 h-5" />
                </div>
                <div>
                  <p className="font-medium text-sm text-base-content">{action.name}</p>
                  <p className="text-xs text-base-content/60">{action.description}</p>
                </div>
              </Link>
            );
          })}
        </div>
      </div>

      {/* Recent Recipes */}
      <div className="bg-base-200 rounded-xl p-6 border border-base-300">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-base-content">Recent Recipes</h2>
          <Link 
            href="/dashboard/recipes" 
            className="text-sm text-warning hover:text-warning/80 font-medium"
          >
            View all →
          </Link>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {recentRecipes.map((recipe) => (
            <div key={recipe.id} className="bg-base-100 rounded-lg overflow-hidden border border-base-300 hover:shadow-lg transition-shadow">
              <div className="aspect-video bg-base-300 relative">
                <img 
                  src={recipe.image} 
                  alt={recipe.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="font-medium text-base-content mb-2">{recipe.name}</h3>
                <div className="flex items-center justify-between text-sm text-base-content/60">
                  <div className="flex items-center gap-1">
                    <ClockIcon className="w-4 h-4" />
                    <span>{recipe.cookTime}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span>⭐</span>
                    <span>{recipe.rating}</span>
                  </div>
                </div>
                <div className="mt-2">
                  <span className="inline-block px-2 py-1 text-xs bg-warning/10 text-warning rounded-full">
                    {recipe.difficulty}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* This Week's Meal Plan Preview */}
      <div className="bg-base-200 rounded-xl p-6 border border-base-300">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-base-content">This Week's Meal Plan</h2>
          <Link 
            href="/dashboard/meal-planner" 
            className="text-sm text-warning hover:text-warning/80 font-medium"
          >
            View full plan →
          </Link>
        </div>
        <div className="text-center py-8 text-base-content/60">
          <CalendarDaysIcon className="w-12 h-12 mx-auto mb-3 opacity-50" />
          <p className="mb-4">No meal plan set for this week yet.</p>
          <Link 
            href="/dashboard/meal-planner" 
            className="btn btn-warning btn-sm"
          >
            Create Meal Plan
          </Link>
        </div>
      </div>
    </div>
  );
}
