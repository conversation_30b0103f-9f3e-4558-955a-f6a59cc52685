"use client";

import { useRef, useState } from "react";
import type { JSX } from "react";

// <FAQ> component is a lsit of <Item> component
// Just import the FAQ & add your FAQ content to the const faqList arrayy below.

interface FAQItemProps {
  question: string;
  answer: JSX.Element;
}

const faqList: FAQItemProps[] = [
  {
    question: "What exactly is Oven350?",
    answer: <div className="space-y-2 leading-relaxed">OVEN350 is an all-in-one culinary platform designed for the modern home cook. It combines powerful recipe management, intelligent meal planning, automated grocery list creation, and direct integration with smart kitchen appliances to streamline your entire cooking process, from inspiration to plate.</div>,
  },
  {
    question: "Who is Oven350 for?",
    answer: (
      <p>
        OVEN350 is for anyone who wants to bring more organization and joy to their kitchen. It&apos;s perfect for busy individuals and families looking to simplify weekly meal planning, avid home cooks who want a central place for all their recipes, and tech-savvy foodies who want to get the most out of their smart kitchen appliances.
      </p>
    ),
  },
  {
    question: "How much does Oven350 cost?",
    answer: (
      <div className="space-y-2 leading-relaxed">
        <p>We offer two tiers to fit your needs:</p>
        <ul className="list-disc pl-5">
          <li>
            <strong>Free Plan:</strong> Perfect for getting started. You can save up to 50 recipes, create weekly meal plans, and explore all our core features.
          </li>
          <li>
            <strong>Oven350 Premium:</strong> For the ultimate culinary experience. This plan offers unlimited recipe storage, advanced nutritional information, exclusive access to new smart kitchen integrations, and priority support.
          </li>
        </ul>
        <p>You can find our current pricing above.</p>
      </div>
    ),
  },
  {
    question: "How is Oven350 different from other recipe apps?",
    answer: (
      <div className="space-y-2 leading-relaxed">While many apps let you save recipes, OVEN350 is a complete kitchen command center. Our key differentiator is the direct smart kitchen integration, which allows the app to communicate with your appliances. This, combined with a seamless recipe manager and an automated grocery list generator, creates a single, connected workflow that other apps don&apos;t offer.</div>
    ),
  },
  {
    question: "How do I get recipes into Oven350?",
    answer: (
      <div className="space-y-2 leading-relaxed">
        <p>You can add recipes in three ways:</p>
        <ol className="list-decimal pl-5">
          <li>
            <strong>Manual Entry:</strong> Add your own custom recipes, like cherished family secrets, using our simple editor.
          </li>
          <li>
            <strong>Web Importer:</strong> Use our browser extension or in-app tool to pull recipes from almost any website instantly.
          </li>
          <li>
            <strong>Our Blog:</strong> Save exclusive, chef-tested recipes directly from the OVEN350 blog with a single click.
          </li>
        </ol>
        <p>All recipes are stored in your personal library, accessible across all your devices.</p>
      </div>
    ),
  },
  {
    question: "Do I need a smart kitchen to use Oven350?",
    answer: (
      <div className="space-y-2 leading-relaxed">Absolutely not! While the smart integrations are a powerful feature, the core recipe management, meal planning, and grocery list tools are designed to streamline the cooking process for anyone, in any kitchen. You can enjoy all these organizational benefits without a single smart appliance.</div>
    ),
  },
  {
    question: "Which smart kitchen devices are compatible with Oven350?",
    answer: (
      <div className="space-y-2 leading-relaxed">We are constantly expanding our ecosystem of compatible devices. Currently, OVEN350 integrates with a wide range of smart ovens, refrigerators, and countertop appliances from leading brands like Bosch, Samsung, LG, and GE Appliances. For a complete and up-to-date list, please visit the &apos;Integrations&apos; tab in your account settings.</div>
    ),
  },
  {
    question: "How does the meal planner and grocery list work?",
    answer: (
      <div className="space-y-2 leading-relaxed">Simply drag and drop your saved recipes onto the weekly calendar to plan your meals. As you add meals, OVEN350 automatically analyzes the ingredients and compiles them into a single, categorized shopping list. You can check off items you already have, add your own personal items &lpar;like paper towels or soap&rpar;, and take the organized list with you on your phone.</div>
    ),
  },
  {
    question: "Can I publish my own recipes on Oven350 Blog?",
    answer: (
      <div className="space-y-2 leading-relaxed">The OVEN350 blog is a source of culinary inspiration curated by our in-house team of chefs and recipe developers. While users cannot publish to the official blog, we provide you with all the tools to manage and organize your own personal collection of recipes, which you can keep private or share with friends and family via a direct link.</div>
    ),
  },
  {
    question: "What devices can I use Oven350 on?",
    answer: (
      <div className="space-y-2 leading-relaxed">OVEN350 is a web-based platform, so you can access it on any device with an internet browser, including desktops, laptops, tablets, and smartphones. Our website is fully responsive for a seamless mobile experience.</div>
    ),
  },
  {
    question: "How do I manage my subscription or cancel my Premium plan?",
    answer: (
      <div className="space-y-2 leading-relaxed">You have full control over your subscription. You can upgrade, downgrade, or cancel your plan at any time from the &apos;Billing&apos; section within your account settings. If you cancel, you will retain access to Premium features until the end of your current billing cycle.</div>
    ),
  },
  {
    question: "Where can I get support if I have a problem?",
    answer: (
      <div className="space-y-2 leading-relaxed">We&apos;re here to help! You can visit our Help Center for detailed guides and tutorials. If you can&apos;t find your answer there, you can contact our support team directly through the &apos;Support&apos; link in the app. Premium subscribers receive priority support.</div>
    ),
  },
];

const FaqItem = ({ item }: { item: FAQItemProps }) => {
  const accordion = useRef(null);
  const [isOpen, setIsOpen] = useState(false);

  return (
    <li>
      <button
        className="relative flex gap-2 items-center w-full py-5 text-base font-semibold text-left border-t md:text-lg border-base-content/10"
        onClick={(e) => {
          e.preventDefault();
          setIsOpen(!isOpen);
        }}
        aria-expanded={isOpen}
      >
        <span
          className={`flex-1 text-base-content ${isOpen ? "text-warning" : ""}`}
        >
          {item?.question}
        </span>
        <svg
          className={`flex-shrink-0 w-4 h-4 ml-auto fill-current`}
          viewBox="0 0 16 16"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            y="7"
            width="16"
            height="2"
            rx="1"
            className={`transform origin-center transition duration-200 ease-out ${
              isOpen && "rotate-180"
            }`}
          />
          <rect
            y="7"
            width="16"
            height="2"
            rx="1"
            className={`transform origin-center rotate-90 transition duration-200 ease-out ${
              isOpen && "rotate-180 hidden"
            }`}
          />
        </svg>
      </button>

      <div
        ref={accordion}
        className={`transition-all duration-300 ease-in-out opacity-80 overflow-hidden`}
        style={
          isOpen
            ? { maxHeight: accordion?.current?.scrollHeight, opacity: 1 }
            : { maxHeight: 0, opacity: 0 }
        }
      >
        <div className="pb-5 leading-relaxed">{item?.answer}</div>
      </div>
    </li>
  );
};

const FAQ = () => {
  return (
    <section className="bg-base-200" id="faq">
      <div className="py-24 px-8 max-w-7xl mx-auto flex flex-col md:flex-row gap-12">
        <div className="flex flex-col text-left basis-1/2">
          <p className="inline-block font-semibold text-warning mb-4">FAQ</p>
          <p className="sm:text-4xl text-3xl font-extrabold text-base-content">
            Frequently Asked Questions
          </p>
        </div>

        <ul className="basis-1/2">
          {faqList.map((item, i) => (
            <FaqItem key={i} item={item} />
          ))}
        </ul>
      </div>
    </section>
  );
};

export default FAQ;
