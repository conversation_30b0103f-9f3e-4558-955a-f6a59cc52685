import mongoose from "mongoose";
import toJSO<PERSON> from "./plugins/toJSON";

// Meal entry schema for individual meals
const mealEntrySchema = new mongoose.Schema({
  recipeId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Recipe',
    required: true,
  },
  servings: {
    type: Number,
    required: true,
    min: 1,
    default: 1,
  },
  notes: {
    type: String,
    trim: true,
    maxlength: 500,
  },
  isCompleted: {
    type: Boolean,
    default: false,
  },
  completedAt: {
    type: Date,
  },
});

// Daily meal plan schema
const dailyMealPlanSchema = new mongoose.Schema({
  date: {
    type: Date,
    required: true,
  },
  breakfast: [mealEntrySchema],
  lunch: [mealEntrySchema],
  dinner: [mealEntrySchema],
  snacks: [mealEntrySchema],
  notes: {
    type: String,
    trim: true,
    maxlength: 1000,
  },
  totalCalories: {
    type: Number,
    min: 0,
    default: 0,
  },
  isCompleted: {
    type: Boolean,
    default: false,
  },
});

// Main MealPlan schema
const mealPlanSchema = new mongoose.Schema(
  {
    // Basic Information
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    
    // User and Sharing
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    
    // Plan Details
    startDate: {
      type: Date,
      required: true,
    },
    endDate: {
      type: Date,
      required: true,
    },
    
    // Daily meal plans
    dailyPlans: [dailyMealPlanSchema],
    
    // Plan Type and Theme
    planType: {
      type: String,
      enum: ['weekly', 'monthly', 'custom'],
      default: 'weekly',
    },
    theme: {
      type: String,
      enum: ['balanced', 'vegetarian', 'vegan', 'keto', 'paleo', 'mediterranean', 'low-carb', 'high-protein', 'family-friendly', 'quick-meals', 'budget-friendly', 'custom'],
      default: 'balanced',
    },
    
    // Dietary Preferences
    dietaryRestrictions: [{
      type: String,
      enum: ['vegetarian', 'vegan', 'gluten-free', 'dairy-free', 'nut-free', 'keto', 'paleo', 'low-carb', 'low-fat'],
    }],
    
    // Nutritional Goals
    nutritionalGoals: {
      dailyCalories: { type: Number, min: 0 },
      dailyProtein: { type: Number, min: 0 }, // in grams
      dailyCarbs: { type: Number, min: 0 }, // in grams
      dailyFat: { type: Number, min: 0 }, // in grams
    },
    
    // Shopping and Preparation
    groceryListGenerated: {
      type: Boolean,
      default: false,
    },
    groceryListId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'GroceryList',
    },
    
    // Meal Prep Information
    mealPrepNotes: {
      type: String,
      trim: true,
      maxlength: 2000,
    },
    prepSchedule: [{
      task: { type: String, required: true },
      day: { type: String, required: true }, // day of week or date
      estimatedTime: { type: Number, min: 0 }, // in minutes
      isCompleted: { type: Boolean, default: false },
    }],
    
    // Template and Sharing
    isTemplate: {
      type: Boolean,
      default: false,
    },
    isPublic: {
      type: Boolean,
      default: false,
    },
    templateName: {
      type: String,
      trim: true,
    },
    
    // Usage and Statistics
    timesUsed: {
      type: Number,
      min: 0,
      default: 0,
    },
    lastUsed: {
      type: Date,
    },
    
    // Status
    status: {
      type: String,
      enum: ['draft', 'active', 'completed', 'archived'],
      default: 'draft',
    },
    
    // Progress Tracking
    completionPercentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
    
    // Budget Information
    estimatedCost: {
      type: Number,
      min: 0,
    },
    actualCost: {
      type: Number,
      min: 0,
    },
    
    // Ratings and Feedback
    rating: {
      type: Number,
      min: 1,
      max: 5,
    },
    feedback: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    
    // Auto-generation settings
    autoGenerationSettings: {
      preferredCuisines: [{ type: String }],
      maxCookTime: { type: Number, min: 0 }, // in minutes
      maxDifficulty: { type: String, enum: ['Easy', 'Medium', 'Hard'] },
      avoidRecentRecipes: { type: Boolean, default: true },
      balanceNutrition: { type: Boolean, default: true },
      includeFavorites: { type: Boolean, default: true },
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

// Indexes for better query performance
mealPlanSchema.index({ userId: 1, status: 1 });
mealPlanSchema.index({ startDate: 1, endDate: 1 });
mealPlanSchema.index({ planType: 1, theme: 1 });
mealPlanSchema.index({ isTemplate: 1, isPublic: 1 });
mealPlanSchema.index({ createdAt: -1 });

// Virtual for plan duration in days
mealPlanSchema.virtual('durationDays').get(function() {
  const diffTime = Math.abs(this.endDate - this.startDate);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
});

// Virtual for total recipes count
mealPlanSchema.virtual('totalRecipes').get(function() {
  return this.dailyPlans.reduce((total, day) => {
    return total + day.breakfast.length + day.lunch.length + day.dinner.length + day.snacks.length;
  }, 0);
});

// Method to calculate completion percentage
mealPlanSchema.methods.calculateCompletionPercentage = function() {
  const totalMeals = this.dailyPlans.reduce((total, day) => {
    return total + day.breakfast.length + day.lunch.length + day.dinner.length + day.snacks.length;
  }, 0);
  
  if (totalMeals === 0) {
    this.completionPercentage = 0;
    return;
  }
  
  const completedMeals = this.dailyPlans.reduce((total, day) => {
    const dayCompleted = day.breakfast.filter(m => m.isCompleted).length +
                        day.lunch.filter(m => m.isCompleted).length +
                        day.dinner.filter(m => m.isCompleted).length +
                        day.snacks.filter(m => m.isCompleted).length;
    return total + dayCompleted;
  }, 0);
  
  this.completionPercentage = Math.round((completedMeals / totalMeals) * 100);
};

// Method to add meal to specific day and meal type
mealPlanSchema.methods.addMeal = function(date: Date, mealType: string, recipeId: string, servings: number = 1, notes?: string) {
  const dateStr = date.toISOString().split('T')[0];
  let dailyPlan = this.dailyPlans.find(dp => dp.date.toISOString().split('T')[0] === dateStr);
  
  if (!dailyPlan) {
    dailyPlan = {
      date: date,
      breakfast: [],
      lunch: [],
      dinner: [],
      snacks: [],
      notes: '',
      totalCalories: 0,
      isCompleted: false,
    };
    this.dailyPlans.push(dailyPlan);
  }
  
  const mealEntry = {
    recipeId,
    servings,
    notes: notes || '',
    isCompleted: false,
  };
  
  if (mealType === 'breakfast') dailyPlan.breakfast.push(mealEntry);
  else if (mealType === 'lunch') dailyPlan.lunch.push(mealEntry);
  else if (mealType === 'dinner') dailyPlan.dinner.push(mealEntry);
  else if (mealType === 'snacks') dailyPlan.snacks.push(mealEntry);
};

// Method to remove meal
mealPlanSchema.methods.removeMeal = function(date: Date, mealType: string, mealIndex: number) {
  const dateStr = date.toISOString().split('T')[0];
  const dailyPlan = this.dailyPlans.find(dp => dp.date.toISOString().split('T')[0] === dateStr);
  
  if (dailyPlan && dailyPlan[mealType] && dailyPlan[mealType][mealIndex]) {
    dailyPlan[mealType].splice(mealIndex, 1);
  }
};

// Method to mark meal as completed
mealPlanSchema.methods.completeMeal = function(date: Date, mealType: string, mealIndex: number) {
  const dateStr = date.toISOString().split('T')[0];
  const dailyPlan = this.dailyPlans.find(dp => dp.date.toISOString().split('T')[0] === dateStr);
  
  if (dailyPlan && dailyPlan[mealType] && dailyPlan[mealType][mealIndex]) {
    dailyPlan[mealType][mealIndex].isCompleted = true;
    dailyPlan[mealType][mealIndex].completedAt = new Date();
    this.calculateCompletionPercentage();
  }
};

// Pre-save middleware
mealPlanSchema.pre('save', function(next) {
  this.calculateCompletionPercentage();
  next();
});

// Add plugin that converts mongoose to json
mealPlanSchema.plugin(toJSON);

export default mongoose.models.MealPlan || mongoose.model("MealPlan", mealPlanSchema);
