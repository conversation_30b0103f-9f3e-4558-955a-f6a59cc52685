import Link from "next/link";
import { getSEOTags } from "@/libs/seo";
import config from "@/config";

// CHATGPT PROMPT TO GENERATE YOUR TERMS & SERVICES — replace with your own data 👇

// 1. Go to https://chat.openai.com/
// 2. Copy paste bellow
// 3. Replace the data with your own (if needed)
// 4. Paste the answer from ChatGPT directly in the <pre> tag below

// You are an excellent lawyer.

// I need your help to write a simple Terms & Services for my website. Here is some context:
// - Website: https://shipfa.st
// - Name: ShipFast
// - Contact information: <EMAIL>
// - Description: A JavaScript code boilerplate to help entrepreneurs launch their startups faster
// - Ownership: when buying a package, users can download code to create apps. They own the code but they do not have the right to resell it. They can ask for a full refund within 7 day after the purchase.
// - User data collected: name, email and payment information
// - Non-personal data collection: web cookies
// - Link to privacy-policy: https://shipfa.st/privacy-policy
// - Governing Law: France
// - Updates to the Terms: users will be updated by email

// Please write a simple Terms & Services for my site. Add the current date. Do not add or explain your reasoning. Answer:

export const metadata = getSEOTags({
  title: `Terms of Service | ${config.appName}`,
  canonicalUrlRelative: "/tos",
});

const TOS = () => {
  return (
    <main className="max-w-xl mx-auto">
      <div className="p-5">
        <Link href="/" className="btn btn-ghost">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
            className="w-5 h-5"
          >
            <path
              fillRule="evenodd"
              d="M15 10a.75.75 0 01-.75.75H7.612l2.158 1.96a.75.75 0 11-1.04 1.08l-3.5-3.25a.75.75 0 010-1.08l3.5-3.25a.75.75 0 111.04 1.08L7.612 9.25h6.638A.75.75 0 0115 10z"
              clipRule="evenodd"
            />
          </svg>
          Back
        </Link>
        <h1 className="text-3xl font-extrabold pb-6">
          Terms of Service for {config.appName}
        </h1>
        <p className="tos-update">Last Updated: 2025-06-16</p>
        {/* <pre
          className="leading-relaxed whitespace-pre-wrap"
          style={{ fontFamily: "sans-serif" }}
        >
          {`Last Updated: September 26, 2023

Welcome to ShipFast!

These Terms of Service ("Terms") govern your use of the ShipFast website at https://shipfa.st ("Website") and the services provided by ShipFast. By using our Website and services, you agree to these Terms.

1. Description of ShipFast

ShipFast is a platform that offers a JavaScript code boilerplate to assist entrepreneurs in launching their startups more efficiently.

2. Ownership and Usage Rights

When you purchase a package from ShipFast, you gain the right to download and use the code provided for creating applications. You own the code you create but do not have the right to resell it. We offer a full refund within 7 days of purchase, as specified in our refund policy.

3. User Data and Privacy

We collect and store user data, including name, email, and payment information, as necessary to provide our services. For details on how we handle your data, please refer to our Privacy Policy at https://shipfa.st/privacy-policy.

4. Non-Personal Data Collection

We use web cookies to collect non-personal data for the purpose of improving our services and user experience.

5. Governing Law

These Terms are governed by the laws of France.

6. Updates to the Terms

We may update these Terms from time to time. Users will be notified of any changes via email.

For any questions or concerns regarding these Terms of Service, please contact <NAME_EMAIL>.

Thank you for using ShipFast!`}
        </pre> */}

        <div className="legal-warning">
          <strong>Important:</strong> This is a legally binding agreement. By creating an account or using the OVEN350 service, you agree to be bound by these terms.
        </div>
        <div className="tos-intro">
        <p>Welcome to OVEN350! These Terms of Service ("Terms") govern your access to and use of the OVEN350 website, applications, and services (collectively, the "Service"). The Service is provided by {config.appName}, a New Jersey limited liability company ("OVEN350," "we," "us," or "our").</p>
        <p>Please read these Terms carefully. By creating an account or by accessing or using our Service, you agree to be bound by these Terms and our Privacy Policy. If you do not agree to these Terms, you may not access or use the Service.</p>
        </div>
        <section className="tos-section" id="description-of-service">
          <h2>1. Description of Service</h2>
          <p>OVEN350 provides a culinary platform that includes, but is not limited to, recipe management, meal planning, automated grocery list generation, and integration with third-party smart kitchen appliances. The Service is designed to help users organize their cooking life and discover new culinary inspiration through our official blog.</p>
        </section>

        <section className="tos-section" id="user-accounts">
          <h2>2. User Accounts</h2>
          <ul>
            <li><strong>Account Creation:</strong> To use most features of the Service, you must register for an account. You agree to provide accurate, current, and complete information during the registration process.</li>
            <li><strong>Age Requirement:</strong> You must be at least 18 years old to create an account and use the Service. Individuals between the ages of 13 and 18 may use the Service only with the permission and under the supervision of a parent or legal guardian who agrees to be bound by these Terms.</li>
            <li><strong>Account Security:</strong> You are responsible for safeguarding your password and for all activities that occur under your account. You must notify us immediately of any unauthorized use of your account.</li>
          </ul>
        </section>

        <section className="tos-section" id="user-content">
          <h2>3. User-Generated Content</h2>
           <ul>
            <li><strong>Your Content:</strong> You retain ownership of all the recipes, notes, and other content you create, upload, or store on the Service ("User Content").</li>
            <li><strong>License to Us:</strong> By providing User Content to the Service, you grant OVEN350 a worldwide, non-exclusive, royalty-free, sublicensable, and transferable license to use, host, store, reproduce, modify, display, and distribute your User Content. This license is for the limited purpose of operating, developing, providing, and improving the Service. This license ends when you delete your User Content or your account.</li>
            <li><strong>Prohibited Content:</strong> You agree not to upload User Content that: (i) infringes any third party's intellectual property rights; (ii) is defamatory, abusive, or obscene; or (iii) violates any law or regulation. We reserve the right to remove any User Content that violates these Terms.</li>
          </ul>
        </section>

        <section className="tos-section" id="subscriptions-payment">
            <h2>4. Subscriptions and Payment</h2>
            <ul>
                <li><strong>Free and Premium Tiers:</strong> We offer both a free and a paid "Premium" subscription plan. Features are detailed on our pricing page.</li>
                <li><strong>Billing:</strong> Premium subscriptions are billed on a recurring basis. By purchasing a Premium subscription, you authorize us to charge your chosen payment method automatically at the start of each billing period.</li>
                <li><strong>Price Changes:</strong> We reserve the right to change our subscription fees. We will provide you with at least 30 days' advance notice of any price changes.</li>
                <li><strong>Cancellation:</strong> You may cancel your Premium subscription at any time through your account settings. Your cancellation will take effect at the end of the current billing cycle. <strong>No refunds will be issued for partial subscription periods.</strong></li>
                <li><strong>Refunds:</strong> We offer a 14-day money-back guarantee for your first Premium subscription payment. For details, please contact our support team.</li>
            </ul>
        </section>

        <section className="tos-section" id="acceptable-use">
            <h2>5. Acceptable Use</h2>
            <p>You agree not to misuse the Service. For example, you must not attempt to reverse engineer the software, use the Service for any illegal purpose, or disrupt the servers or networks connected to the Service.</p>
        </section>

        <section className="tos-section" id="third-party-services">
            <h2>6. Third-Party Services</h2>
            <p>The Service may allow you to access or integrate with third-party services and devices. We do not control and are not responsible for these third-party services. Your use is subject to their own terms and policies.</p>
        </section>

        <section className="tos-section" id="intellectual-property">
            <h2>7. Intellectual Property</h2>
            <p>Excluding your User Content, all aspects of the Service are the exclusive property of {config.appName} and its licensors.</p>
        </section>

        <section className="tos-section" id="termination">
            <h2>8. Termination</h2>
            <p>You are free to stop using our Service at any time. We reserve the right to suspend or terminate your access to the Service with or without notice if you violate these Terms. Upon termination, we may delete your account and all associated User Content.</p>
        </section>
        
        <section className="tos-section" id="disclaimers">
            <h2>9. Disclaimers and Limitation of Liability</h2>
            <p><strong>"AS IS" SERVICE:</strong> THE SERVICE IS PROVIDED ON AN "AS IS" AND "AS AVAILABLE" BASIS, WITHOUT ANY WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED.</p>
            <p><strong>LIMITATION OF LIABILITY:</strong> TO THE FULLEST EXTENT PERMITTED BY LAW, IN NO EVENT SHALL OVEN350 BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, OR CONSEQUENTIAL DAMAGES. IN NO EVENT SHALL OVEN350'S AGGREGATE LIABILITY EXCEED THE GREATER OF ONE HUNDRED U.S. DOLLARS ($100.00) OR THE AMOUNT YOU PAID OVEN350, IF ANY, IN THE PAST SIX MONTHS.</p>
        </section>

        <section className="tos-section" id="governing-law">
            <h2>10. Governing Law and Dispute Resolution</h2>
            <ul>
                <li><strong>Governing Law:</strong> These Terms shall be governed by the laws of the State of New Jersey.</li>
                <li><strong>Dispute Resolution:</strong> In the event of a dispute, you agree to first contact us at {config.resend.supportEmail} to attempt to resolve the issue informally. If unresolved, we each agree to resolve any claim by binding arbitration in Burlington County, New Jersey.</li>
                <li><strong>CLASS ACTION WAIVER:</strong> YOU AGREE TO ONLY BRING CLAIMS AGAINST OVEN350 IN YOUR INDIVIDUAL CAPACITY AND NOT AS A PLAINTIFF OR CLASS MEMBER IN ANY PURPORTED CLASS OR REPRESENTATIVE PROCEEDING.</li>
            </ul>
        </section>

        <section className="tos-section" id="general-terms">
            <h2>11. General Terms</h2>
            <ul>
                <li><strong>Entire Agreement:</strong> These Terms constitute the entire agreement between you and OVEN350.</li>
                <li><strong>Changes to Terms:</strong> We may modify these Terms from time to time. Your continued use of the Service after changes become effective constitutes your acceptance of the new Terms.</li>
                <li><strong>Severability:</strong> If any provision of these Terms is found to be unenforceable, the remaining provisions will remain in full force and effect.</li>
            </ul>
        </section>

        <section className="tos-section" id="contact">
            <h2>12. Contact Us</h2>
            <p>If you have any questions about these Terms, please contact us at:</p>
            <address>
              {config.appName}<br />
              {config.appAddress}<br />
              Email: {config.resend.supportEmail}
            </address>
        </section>
      </div>
    </main>
  );
};

export default TOS;
