import mongoose from "mongoose";
import to<PERSON><PERSON><PERSON> from "./plugins/toJSON";

// Dietary preferences schema
const dietaryPreferencesSchema = new mongoose.Schema({
  restrictions: [{
    type: String,
    enum: ['vegetarian', 'vegan', 'gluten-free', 'dairy-free', 'nut-free', 'keto', 'paleo', 'low-carb', 'low-fat', 'halal', 'kosher'],
  }],
  allergies: [{
    allergen: { type: String, required: true },
    severity: { type: String, enum: ['mild', 'moderate', 'severe'], default: 'moderate' },
  }],
  dislikes: [{
    type: String,
    trim: true,
  }],
  preferences: [{
    type: String,
    trim: true,
  }],
});

// Cooking preferences schema
const cookingPreferencesSchema = new mongoose.Schema({
  skillLevel: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    default: 'intermediate',
  },
  maxCookTime: {
    type: Number, // in minutes
    min: 0,
    default: 60,
  },
  maxPrepTime: {
    type: Number, // in minutes
    min: 0,
    default: 30,
  },
  preferredDifficulty: [{
    type: String,
    enum: ['Easy', 'Medium', 'Hard'],
  }],
  favoriteEquipment: [{
    type: String,
    trim: true,
  }],
  availableEquipment: [{
    equipment: { type: String, required: true },
    hasAccess: { type: Boolean, default: true },
  }],
  cookingMethods: [{
    method: { type: String, required: true },
    preference: { type: String, enum: ['love', 'like', 'neutral', 'dislike', 'avoid'], default: 'neutral' },
  }],
});

// Meal planning preferences schema
const mealPlanningPreferencesSchema = new mongoose.Schema({
  defaultServings: {
    type: Number,
    min: 1,
    max: 20,
    default: 4,
  },
  mealPlanDuration: {
    type: String,
    enum: ['weekly', 'biweekly', 'monthly'],
    default: 'weekly',
  },
  preferredMealTypes: [{
    type: String,
    enum: ['breakfast', 'lunch', 'dinner', 'snack', 'dessert'],
  }],
  budgetPerWeek: {
    type: Number,
    min: 0,
  },
  shoppingDay: {
    type: String,
    enum: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
    default: 'sunday',
  },
  mealPrepDay: {
    type: String,
    enum: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'],
    default: 'sunday',
  },
  autoGeneratePlans: {
    type: Boolean,
    default: false,
  },
  includeLeftovers: {
    type: Boolean,
    default: true,
  },
  varietyPreference: {
    type: String,
    enum: ['high', 'medium', 'low'], // how much variety in meal plans
    default: 'medium',
  },
});

// Nutritional goals schema
const nutritionalGoalsSchema = new mongoose.Schema({
  dailyCalories: {
    target: { type: Number, min: 0 },
    min: { type: Number, min: 0 },
    max: { type: Number, min: 0 },
  },
  macroTargets: {
    protein: { type: Number, min: 0 }, // percentage
    carbs: { type: Number, min: 0 }, // percentage
    fat: { type: Number, min: 0 }, // percentage
  },
  dailyNutrients: {
    fiber: { type: Number, min: 0 }, // grams
    sodium: { type: Number, min: 0 }, // mg
    sugar: { type: Number, min: 0 }, // grams
  },
  healthGoals: [{
    type: String,
    enum: ['weight-loss', 'weight-gain', 'muscle-gain', 'heart-health', 'diabetes-management', 'general-wellness'],
  }],
  trackNutrition: {
    type: Boolean,
    default: false,
  },
});

// Kitchen setup schema
const kitchenSetupSchema = new mongoose.Schema({
  kitchenSize: {
    type: String,
    enum: ['small', 'medium', 'large'],
    default: 'medium',
  },
  storageSpace: {
    pantry: { type: String, enum: ['none', 'small', 'medium', 'large'], default: 'medium' },
    freezer: { type: String, enum: ['none', 'small', 'medium', 'large'], default: 'medium' },
    refrigerator: { type: String, enum: ['small', 'medium', 'large'], default: 'medium' },
  },
  smartDevices: [{
    device: { type: String, required: true },
    brand: { type: String },
    model: { type: String },
    isConnected: { type: Boolean, default: false },
    capabilities: [{ type: String }],
  }],
  preferredStores: [{
    name: { type: String, required: true },
    type: { type: String, enum: ['grocery', 'specialty', 'online', 'farmers-market'] },
    location: { type: String },
    isPrimary: { type: Boolean, default: false },
  }],
});

// Main UserPreferences schema
const userPreferencesSchema = new mongoose.Schema(
  {
    // User Reference
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      unique: true,
    },
    
    // Preference Categories
    dietary: dietaryPreferencesSchema,
    cooking: cookingPreferencesSchema,
    mealPlanning: mealPlanningPreferencesSchema,
    nutritionalGoals: nutritionalGoalsSchema,
    kitchenSetup: kitchenSetupSchema,
    
    // Cuisine Preferences
    favoriteCuisines: [{
      cuisine: { type: String, required: true },
      preference: { type: String, enum: ['love', 'like', 'neutral', 'dislike', 'avoid'], default: 'like' },
    }],
    
    // Recipe Discovery
    discoveryPreferences: {
      showTrendingRecipes: { type: Boolean, default: true },
      showSeasonalRecipes: { type: Boolean, default: true },
      showCommunityRecipes: { type: Boolean, default: true },
      personalizedRecommendations: { type: Boolean, default: true },
    },
    
    // Notification Preferences
    notifications: {
      mealPlanReminders: { type: Boolean, default: true },
      shoppingListReminders: { type: Boolean, default: true },
      newRecipeAlerts: { type: Boolean, default: false },
      weeklyMealPlanSuggestions: { type: Boolean, default: true },
      nutritionGoalUpdates: { type: Boolean, default: false },
      communityUpdates: { type: Boolean, default: false },
    },
    
    // Display Preferences
    displaySettings: {
      theme: { type: String, enum: ['light', 'dark', 'auto'], default: 'auto' },
      language: { type: String, default: 'en' },
      units: {
        temperature: { type: String, enum: ['fahrenheit', 'celsius'], default: 'fahrenheit' },
        weight: { type: String, enum: ['imperial', 'metric'], default: 'imperial' },
        volume: { type: String, enum: ['imperial', 'metric'], default: 'imperial' },
      },
      dateFormat: { type: String, enum: ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD'], default: 'MM/DD/YYYY' },
      defaultView: {
        recipes: { type: String, enum: ['grid', 'list'], default: 'grid' },
        mealPlanner: { type: String, enum: ['week', 'month'], default: 'week' },
      },
    },
    
    // Privacy Settings
    privacy: {
      profileVisibility: { type: String, enum: ['public', 'friends', 'private'], default: 'private' },
      shareRecipes: { type: Boolean, default: false },
      shareMealPlans: { type: Boolean, default: false },
      allowRecommendations: { type: Boolean, default: true },
      dataCollection: { type: Boolean, default: true },
    },
    
    // Onboarding and Setup
    onboardingCompleted: {
      type: Boolean,
      default: false,
    },
    setupSteps: {
      dietaryPreferences: { type: Boolean, default: false },
      cookingSkills: { type: Boolean, default: false },
      kitchenSetup: { type: Boolean, default: false },
      firstRecipe: { type: Boolean, default: false },
      firstMealPlan: { type: Boolean, default: false },
    },
    
    // Usage Analytics (for personalization)
    usageStats: {
      totalRecipes: { type: Number, default: 0 },
      totalMealPlans: { type: Number, default: 0 },
      favoriteCategories: [{ type: String }],
      mostUsedTags: [{ type: String }],
      averageCookTime: { type: Number, default: 0 },
      lastActiveDate: { type: Date, default: Date.now },
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

// Indexes
userPreferencesSchema.index({ userId: 1 }, { unique: true });

// Method to check if user has dietary restriction
userPreferencesSchema.methods.hasDietaryRestriction = function(restriction: string) {
  return this.dietary.restrictions.includes(restriction);
};

// Method to check if user is allergic to ingredient
userPreferencesSchema.methods.hasAllergy = function(allergen: string) {
  return this.dietary.allergies.some(allergy => 
    allergy.allergen.toLowerCase() === allergen.toLowerCase()
  );
};

// Method to get recipe compatibility score
userPreferencesSchema.methods.getRecipeCompatibilityScore = function(recipe: any) {
  let score = 100;
  
  // Check dietary restrictions
  if (recipe.dietaryRestrictions) {
    const userRestrictions = this.dietary.restrictions;
    const hasConflict = recipe.dietaryRestrictions.some(restriction => 
      !userRestrictions.includes(restriction)
    );
    if (hasConflict) score -= 30;
  }
  
  // Check allergies
  if (recipe.ingredients) {
    const hasAllergen = recipe.ingredients.some(ingredient =>
      this.hasAllergy(ingredient.name)
    );
    if (hasAllergen) score -= 50;
  }
  
  // Check cooking time preferences
  if (recipe.totalTime > this.cooking.maxCookTime) {
    score -= 20;
  }
  
  // Check difficulty preference
  if (this.cooking.preferredDifficulty.length > 0 && 
      !this.cooking.preferredDifficulty.includes(recipe.difficulty)) {
    score -= 15;
  }
  
  // Check cuisine preferences
  const cuisinePreference = this.favoriteCuisines.find(c => c.cuisine === recipe.cuisine);
  if (cuisinePreference) {
    switch (cuisinePreference.preference) {
      case 'love': score += 20; break;
      case 'like': score += 10; break;
      case 'dislike': score -= 15; break;
      case 'avoid': score -= 40; break;
    }
  }
  
  return Math.max(0, Math.min(100, score));
};

// Method to update usage stats
userPreferencesSchema.methods.updateUsageStats = function(action: string, data?: any) {
  this.usageStats.lastActiveDate = new Date();
  
  switch (action) {
    case 'recipe_added':
      this.usageStats.totalRecipes += 1;
      break;
    case 'meal_plan_created':
      this.usageStats.totalMealPlans += 1;
      break;
    // Add more cases as needed
  }
};

// Add plugin that converts mongoose to json
userPreferencesSchema.plugin(toJSON);

export default mongoose.models.UserPreferences || mongoose.model("UserPreferences", userPreferencesSchema);
