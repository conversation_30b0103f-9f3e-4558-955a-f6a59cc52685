import mongoose from "mongoose";
import to<PERSON><PERSON><PERSON> from "./plugins/toJSON";

// Grocery item schema
const groceryItemSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  amount: {
    type: Number,
    required: true,
    min: 0,
  },
  unit: {
    type: String,
    required: true,
    trim: true,
  },
  category: {
    type: String,
    required: true,
    enum: [
      'produce',
      'meat-seafood',
      'dairy-eggs',
      'bakery',
      'pantry',
      'frozen',
      'beverages',
      'snacks',
      'condiments',
      'spices',
      'household',
      'other'
    ],
    default: 'other',
  },
  notes: {
    type: String,
    trim: true,
    maxlength: 200,
  },
  estimatedPrice: {
    type: Number,
    min: 0,
  },
  actualPrice: {
    type: Number,
    min: 0,
  },
  brand: {
    type: String,
    trim: true,
  },
  store: {
    type: String,
    trim: true,
  },
  isChecked: {
    type: Boolean,
    default: false,
  },
  checkedAt: {
    type: Date,
  },
  checkedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
  },
  priority: {
    type: String,
    enum: ['low', 'medium', 'high'],
    default: 'medium',
  },
  // Track which recipes this item is needed for
  recipeIds: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Recipe',
  }],
  // For items that were combined from multiple recipe ingredients
  originalIngredients: [{
    recipeId: { type: mongoose.Schema.Types.ObjectId, ref: 'Recipe' },
    ingredientName: { type: String },
    amount: { type: Number },
    unit: { type: String },
  }],
});

// Store section schema for organizing items by store layout
const storeSectionSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
  },
  categories: [{
    type: String,
    enum: [
      'produce',
      'meat-seafood',
      'dairy-eggs',
      'bakery',
      'pantry',
      'frozen',
      'beverages',
      'snacks',
      'condiments',
      'spices',
      'household',
      'other'
    ],
  }],
  order: {
    type: Number,
    default: 0,
  },
});

// Main GroceryList schema
const groceryListSchema = new mongoose.Schema(
  {
    // Basic Information
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    description: {
      type: String,
      trim: true,
      maxlength: 1000,
    },
    
    // User and Sharing
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    sharedWith: [{
      userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
      permission: { type: String, enum: ['view', 'edit'], default: 'view' },
      sharedAt: { type: Date, default: Date.now },
    }],
    
    // List Items
    items: [groceryItemSchema],
    
    // Organization
    groupByCategory: {
      type: Boolean,
      default: true,
    },
    customSections: [storeSectionSchema],
    
    // Source Information
    mealPlanId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'MealPlan',
    },
    recipeIds: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Recipe',
    }],
    generatedFrom: {
      type: String,
      enum: ['meal-plan', 'recipes', 'manual', 'template'],
      default: 'manual',
    },
    
    // Shopping Information
    targetStore: {
      type: String,
      trim: true,
    },
    estimatedTotal: {
      type: Number,
      min: 0,
      default: 0,
    },
    actualTotal: {
      type: Number,
      min: 0,
    },
    
    // Shopping Session
    shoppingStarted: {
      type: Boolean,
      default: false,
    },
    shoppingStartedAt: {
      type: Date,
    },
    shoppingCompletedAt: {
      type: Date,
    },
    
    // Status and Progress
    status: {
      type: String,
      enum: ['draft', 'ready', 'shopping', 'completed', 'archived'],
      default: 'draft',
    },
    completionPercentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 0,
    },
    
    // Template Information
    isTemplate: {
      type: Boolean,
      default: false,
    },
    templateName: {
      type: String,
      trim: true,
    },
    templateCategory: {
      type: String,
      enum: ['weekly-staples', 'monthly-bulk', 'party-supplies', 'holiday', 'custom'],
    },
    
    // Recurring List
    isRecurring: {
      type: Boolean,
      default: false,
    },
    recurringSchedule: {
      frequency: { type: String, enum: ['weekly', 'biweekly', 'monthly'] },
      dayOfWeek: { type: Number, min: 0, max: 6 }, // 0 = Sunday
      dayOfMonth: { type: Number, min: 1, max: 31 },
      nextGeneration: { type: Date },
    },
    
    // Budget and Savings
    budgetLimit: {
      type: Number,
      min: 0,
    },
    coupons: [{
      description: { type: String, required: true },
      discount: { type: Number, min: 0 },
      discountType: { type: String, enum: ['percentage', 'fixed'], default: 'fixed' },
      itemIds: [{ type: mongoose.Schema.Types.ObjectId }],
      isUsed: { type: Boolean, default: false },
      expiresAt: { type: Date },
    }],
    
    // Notes and Reminders
    notes: {
      type: String,
      trim: true,
      maxlength: 2000,
    },
    reminders: [{
      message: { type: String, required: true },
      reminderDate: { type: Date, required: true },
      isCompleted: { type: Boolean, default: false },
    }],
    
    // Analytics
    averageShoppingTime: {
      type: Number, // in minutes
      min: 0,
    },
    frequentItems: [{
      itemName: { type: String },
      frequency: { type: Number, min: 0 },
    }],
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
  }
);

// Indexes for better query performance
groceryListSchema.index({ userId: 1, status: 1 });
groceryListSchema.index({ mealPlanId: 1 });
groceryListSchema.index({ isTemplate: 1, templateCategory: 1 });
groceryListSchema.index({ isRecurring: 1, 'recurringSchedule.nextGeneration': 1 });
groceryListSchema.index({ createdAt: -1 });

// Virtual for total items count
groceryListSchema.virtual('totalItems').get(function() {
  return this.items.length;
});

// Virtual for checked items count
groceryListSchema.virtual('checkedItems').get(function() {
  return this.items.filter(item => item.isChecked).length;
});

// Virtual for unchecked items count
groceryListSchema.virtual('uncheckedItems').get(function() {
  return this.items.filter(item => !item.isChecked).length;
});

// Method to calculate completion percentage
groceryListSchema.methods.calculateCompletionPercentage = function() {
  if (this.items.length === 0) {
    this.completionPercentage = 0;
    return;
  }
  
  const checkedCount = this.items.filter(item => item.isChecked).length;
  this.completionPercentage = Math.round((checkedCount / this.items.length) * 100);
};

// Method to calculate estimated total
groceryListSchema.methods.calculateEstimatedTotal = function() {
  this.estimatedTotal = this.items.reduce((total, item) => {
    return total + (item.estimatedPrice || 0);
  }, 0);
};

// Method to add item
groceryListSchema.methods.addItem = function(itemData: any) {
  // Check if similar item already exists
  const existingItem = this.items.find(item => 
    item.name.toLowerCase() === itemData.name.toLowerCase() && 
    item.unit === itemData.unit
  );
  
  if (existingItem) {
    // Combine quantities
    existingItem.amount += itemData.amount;
    if (itemData.recipeIds) {
      existingItem.recipeIds.push(...itemData.recipeIds);
    }
  } else {
    this.items.push(itemData);
  }
  
  this.calculateEstimatedTotal();
};

// Method to check/uncheck item
groceryListSchema.methods.toggleItem = function(itemId: string, userId?: string) {
  const item = this.items.id(itemId);
  if (item) {
    item.isChecked = !item.isChecked;
    if (item.isChecked) {
      item.checkedAt = new Date();
      if (userId) item.checkedBy = userId;
    } else {
      item.checkedAt = undefined;
      item.checkedBy = undefined;
    }
    this.calculateCompletionPercentage();
  }
};

// Method to organize items by category
groceryListSchema.methods.getItemsByCategory = function() {
  const categories = {};
  
  this.items.forEach(item => {
    if (!categories[item.category]) {
      categories[item.category] = [];
    }
    categories[item.category].push(item);
  });
  
  return categories;
};

// Method to start shopping session
groceryListSchema.methods.startShopping = function() {
  this.shoppingStarted = true;
  this.shoppingStartedAt = new Date();
  this.status = 'shopping';
};

// Method to complete shopping
groceryListSchema.methods.completeShopping = function() {
  this.shoppingCompletedAt = new Date();
  this.status = 'completed';
  
  // Calculate shopping time
  if (this.shoppingStartedAt) {
    const shoppingTime = Math.round((this.shoppingCompletedAt - this.shoppingStartedAt) / (1000 * 60));
    this.averageShoppingTime = shoppingTime;
  }
};

// Pre-save middleware
groceryListSchema.pre('save', function(next) {
  this.calculateCompletionPercentage();
  this.calculateEstimatedTotal();
  next();
});

// Add plugin that converts mongoose to json
groceryListSchema.plugin(toJSON);

export default mongoose.models.GroceryList || mongoose.model("GroceryList", groceryListSchema);
