{"name": "ship-fast-code", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/mongodb-adapter": "^1.0.0", "@headlessui/react": "^1.7.18", "@heroicons/react": "^2.2.0", "@mdx-js/loader": "^2.3.0", "@mdx-js/react": "^2.3.0", "@next/mdx": "^13.5.6", "axios": "^1.6.8", "crisp-sdk-web": "^1.0.22", "eslint": "8.47.0", "eslint-config-next": "13.4.19", "form-data": "^4.0.0", "mongodb": "^5.9.2", "mongoose": "^7.6.10", "next": "^15.3.4", "next-auth": "^5.0.0-beta.29", "next-sitemap": "^4.2.3", "nextjs-toploader": "^1.6.11", "nodemailer": "^6.9.13", "react": "18.2.0", "react-dom": "18.2.0", "react-hot-toast": "^2.4.1", "react-icons": "^5.5.0", "react-syntax-highlighter": "^15.5.0", "react-tooltip": "^5.26.3", "resend": "^4.0.1", "stripe": "^13.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/mdx": "^2.0.12", "@types/mongoose": "^5.11.97", "@types/node": "^20.12.2", "@types/react": "^18.2.73", "@types/react-dom": "^18.2.23", "@types/react-syntax-highlighter": "^15.5.11", "autoprefixer": "^10.4.19", "daisyui": "^4.10.1", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5.4.3"}}