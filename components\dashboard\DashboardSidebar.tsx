"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  HomeIcon,
  BookOpenIcon,
  CalendarDaysIcon,
  ShoppingCartIcon,
  MagnifyingGlassIcon,
  UserIcon,
  Cog6ToothIcon,
  PlusIcon,
  TagIcon,
  ChartBarIcon,
  HeartIcon,
  ShareIcon,
} from "@heroicons/react/24/outline";
import {
  HomeIcon as HomeIconSolid,
  BookOpenIcon as BookOpenIconSolid,
  CalendarDaysIcon as CalendarDaysIconSolid,
  ShoppingCartIcon as ShoppingCartIconSolid,
  MagnifyingGlassIcon as MagnifyingGlassIconSolid,
  UserIcon as UserIconSolid,
  Cog6ToothIcon as Cog6ToothIconSolid,
  PlusIcon as PlusIconSolid,
  TagIcon as TagIconSolid,
  ChartBarIcon as ChartBarIconSolid,
  HeartIcon as HeartIconSolid,
  ShareIcon as ShareIconSolid,
} from "@heroicons/react/24/solid";
import Image from "next/image";

interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  iconSolid: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  badge?: string;
}

const navigation: NavItem[] = [
  {
    name: "Dashboard",
    href: "/dashboard",
    icon: HomeIcon,
    iconSolid: HomeIconSolid,
  },
  {
    name: "My Recipes",
    href: "/dashboard/recipes",
    icon: BookOpenIcon,
    iconSolid: BookOpenIconSolid,
  },
  {
    name: "Add Recipe",
    href: "/dashboard/recipes/add",
    icon: PlusIcon,
    iconSolid: PlusIconSolid,
  },
  {
    name: "Meal Planner",
    href: "/dashboard/meal-planner",
    icon: CalendarDaysIcon,
    iconSolid: CalendarDaysIconSolid,
  },
  {
    name: "Grocery Lists",
    href: "/dashboard/grocery-lists",
    icon: ShoppingCartIcon,
    iconSolid: ShoppingCartIconSolid,
  },
  {
    name: "Search Recipes",
    href: "/dashboard/search",
    icon: MagnifyingGlassIcon,
    iconSolid: MagnifyingGlassIconSolid,
  },
  {
    name: "Categories & Tags",
    href: "/dashboard/categories",
    icon: TagIcon,
    iconSolid: TagIconSolid,
  },
  {
    name: "Favorites",
    href: "/dashboard/favorites",
    icon: HeartIcon,
    iconSolid: HeartIconSolid,
  },
  {
    name: "Shared Recipes",
    href: "/dashboard/shared",
    icon: ShareIcon,
    iconSolid: ShareIconSolid,
  },
  {
    name: "Analytics",
    href: "/dashboard/analytics",
    icon: ChartBarIcon,
    iconSolid: ChartBarIconSolid,
  },
];

const bottomNavigation: NavItem[] = [
  {
    name: "Profile",
    href: "/dashboard/profile",
    icon: UserIcon,
    iconSolid: UserIconSolid,
  },
  {
    name: "Settings",
    href: "/dashboard/settings",
    icon: Cog6ToothIcon,
    iconSolid: Cog6ToothIconSolid,
  },
];

export default function DashboardSidebar() {
  const pathname = usePathname();

  const isActive = (href: string) => {
    return pathname === href;
  };

  return (
    <aside className="w-64 min-h-full bg-base-200 border-r border-base-300">
      <div className="flex flex-col h-full">
        {/* Logo */}
        <div className="flex items-center gap-2 p-6 border-b border-base-300">
          <div className="w-8 h-8 bg-warning rounded-lg flex items-center justify-center relative">
             <Image src="/icon.png" alt="Oven350 Logo" fill className="object-contain rounded" />
          </div>
          <div className="flex flex-col ">
            <h1 className="text-lg font-bold text-base-content leading-[20px]">OVEN350</h1>
            <p className="text-xs text-base-content/60 leading-[18px]">Recipe Manager</p>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6">
          <ul className="space-y-2">
            {navigation.map((item) => {
              const active = isActive(item.href);
              const Icon = active ? item.iconSolid : item.icon;

              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      active
                        ? "bg-warning text-warning-content"
                        : "text-base-content/70 hover:text-base-content hover:bg-base-300"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.name}</span>
                    {item.badge && (
                      <span className="ml-auto bg-error text-error-content text-xs px-2 py-1 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        {/* Bottom Navigation */}
        <div className="px-4 py-4 border-t border-base-300">
          <ul className="space-y-2">
            {bottomNavigation.map((item) => {
              const active = isActive(item.href);
              const Icon = active ? item.iconSolid : item.icon;

              return (
                <li key={item.name}>
                  <Link
                    href={item.href}
                    className={`flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                      active
                        ? "bg-warning text-warning-content"
                        : "text-base-content/70 hover:text-base-content hover:bg-base-300"
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span>{item.name}</span>
                  </Link>
                </li>
              );
            })}
          </ul>
        </div>
      </div>
    </aside>
  );
}
