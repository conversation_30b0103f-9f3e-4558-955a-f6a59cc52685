import MealPlannerCalendar from "@/components/dashboard/meal-planner/MealPlannerCalendar";
import MealPlannerSidebar from "@/components/dashboard/meal-planner/MealPlannerSidebar";

export const dynamic = "force-dynamic";

export default async function MealPlannerPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl lg:text-3xl font-bold text-base-content">Meal Planner</h1>
        <p className="text-base-content/70 mt-1">
          Plan your meals for the week and generate grocery lists automatically
        </p>
      </div>

      {/* Meal Planner Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Sidebar with recipes */}
        <div className="lg:col-span-1">
          <MealPlannerSidebar />
        </div>
        
        {/* Calendar */}
        <div className="lg:col-span-3">
          <MealPlannerCalendar />
        </div>
      </div>
    </div>
  );
}
